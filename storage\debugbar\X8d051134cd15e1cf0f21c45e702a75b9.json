{"__meta": {"id": "X8d051134cd15e1cf0f21c45e702a75b9", "datetime": "2025-07-08 17:01:08", "utime": 1751983268.047182, "method": "POST", "uri": "/livewire/message/etudiant", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751983264.876689, "end": 1751983268.047216, "duration": 3.1705269813537598, "duration_str": "3.17s", "measures": [{"label": "Booting", "start": 1751983264.876689, "relative_start": 0, "end": 1751983265.882773, "relative_end": 1751983265.882773, "duration": 1.0060839653015137, "duration_str": "1.01s", "params": [], "collector": null}, {"label": "Application", "start": 1751983265.883659, "relative_start": 1.006969928741455, "end": 1751983268.047219, "relative_end": 3.0994415283203125e-06, "duration": 2.163560152053833, "duration_str": "2.16s", "params": [], "collector": null}]}, "memory": {"peak_usage": 27584720, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "livewire.deraq.etudiant.notes (\\resources\\views\\livewire\\deraq\\etudiant\\notes.blade.php)", "param_count": 59, "params": ["parcours", "semestres", "livewireLayout", "errors", "_instance", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/etudiant/notes.blade.php&line=0"}, {"name": "components.note-form-modal (\\resources\\views\\components\\note-form-modal.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "parcours", "semestres", "livewireLayout", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "page", "paginators", "__currentLoopData", "loop", "__empty_1", "note", "index", "noteClass"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/note-form-modal.blade.php&line=0"}, {"name": "components.toast-notifications (\\resources\\views\\components\\toast-notifications.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "parcours", "semestres", "livewireLayout", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "page", "paginators", "__currentLoopData", "loop", "__empty_1", "note", "index", "noteClass"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/toast-notifications.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 18, "nb_failed_statements": 0, "accumulated_duration": 1.1791, "accumulated_duration_str": "1.18s", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.005860000000000001, "duration_str": "5.86ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 0.497}, {"sql": "select * from `notes` where `notes`.`id` in (10219, 10179, 10139, 10099, 10059, 10019, 9979, 9939, 9899, 9859, 9819, 9779, 9739, 9699, 9659, 9619, 8520, 8480, 8415, 8356, 8302, 8262, 8012, 7874, 7834, 4575, 4518, 4461, 4368, 4311, 4180, 4123, 3453, 3380, 3323, 3266, 2734, 2677, 2324, 1625, 1568, 1511, 1454, 1397, 1295, 1226, 1163, 1106, 1049, 992, 885, 828, 771, 714, 657)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0016799999999999999, "duration_str": "1.68ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 0.497, "width_percent": 0.142}, {"sql": "select * from `matieres` where `matieres`.`id` in (33, 34, 35, 36, 37, 38, 41, 42, 43, 45, 196, 197, 198, 199, 200, 201, 202, 203) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.00117, "duration_str": "1.17ms", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 0.639, "width_percent": 0.099}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.00146, "duration_str": "1.46ms", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 0.739, "width_percent": 0.124}, {"sql": "select * from `matieres` where `matieres`.`id` in (33, 34, 35, 36, 37, 38, 41, 42, 43, 45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.17198, "duration_str": "172ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 0.863, "width_percent": 14.586}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.14827, "duration_str": "148ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 15.448, "width_percent": 12.575}, {"sql": "select * from `semestres` where `semestres`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0583, "duration_str": "58.3ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 28.023, "width_percent": 4.944}, {"sql": "select * from `niveaux` where `niveaux`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.05067, "duration_str": "50.67ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 32.968, "width_percent": 4.297}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.08063, "duration_str": "80.63ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 37.265, "width_percent": 6.838}, {"sql": "select * from `parcours` where `parcours`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.10159, "duration_str": "102ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 44.103, "width_percent": 8.616}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `semestre_id` = 1 and `parcour_id` = '2' and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 731}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.07889, "duration_str": "78.89ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:731", "connection": "imsaaapp", "start_percent": 52.719, "width_percent": 6.691}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.03411, "duration_str": "34.11ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 59.41, "width_percent": 2.893}, {"sql": "select `id`, `user_id`, `parcour_id`, `niveau_id`, `annee_universitaire_id`, `created_at`, `deleted_at` from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null order by `created_at` desc limit 10 offset 690", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.056729999999999996, "duration_str": "56.73ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 62.303, "width_percent": 4.811}, {"sql": "select `id`, `nom`, `prenom`, `telephone1`, `sexe`, `photo` from `users` where `users`.`id` in (84, 85, 86, 87, 88, 89, 109, 110, 129, 130) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.08294, "duration_str": "82.94ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 67.114, "width_percent": 7.034}, {"sql": "select `id`, `sigle`, `nom` from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0873, "duration_str": "87.3ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 74.148, "width_percent": 7.404}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.09306, "duration_str": "93.06ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 81.552, "width_percent": 7.892}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.07765000000000001, "duration_str": "77.65ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 89.444, "width_percent": 6.586}, {"sql": "select `id`, `nom` from `semestres` where `niveau_id` = 1 and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 204}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.046810000000000004, "duration_str": "46.81ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:204", "connection": "imsaaapp", "start_percent": 96.03, "width_percent": 3.97}]}, "models": {"data": {"App\\Models\\InscriptionStudent": 10, "App\\Models\\Parcour": 2, "App\\Models\\AnneeUniversitaire": 2, "App\\Models\\Niveau": 2, "App\\Models\\Semestre": 3, "App\\Models\\TypeNote": 6, "App\\Models\\Matiere": 37, "App\\Models\\Note": 55, "App\\Models\\User": 11}, "count": 128}, "livewire": {"data": {"etudiant #JbDmk0syBswU4E5bsoUO": "array:5 [\n  \"data\" => array:54 [\n    \"showCreateModal\" => false\n    \"showEditModal\" => false\n    \"showDeleteModal\" => false\n    \"showRestoreModal\" => false\n    \"viewSupprimesMode\" => false\n    \"selectedEtudiantId\" => null\n    \"selectedUserId\" => null\n    \"etuName\" => null\n    \"newUser\" => []\n    \"editUser\" => []\n    \"editParcours\" => []\n    \"query\" => null\n    \"filtreParcours\" => null\n    \"filtreNiveau\" => null\n    \"filtreAnnee\" => null\n    \"perPage\" => 10\n    \"isLoading\" => false\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"showFilters\" => true\n    \"compactView\" => false\n    \"bulkAction\" => \"\"\n    \"totalEtudiants\" => 720\n    \"totalSupprimés\" => 102\n    \"selectedItems\" => []\n    \"showNotes\" => true\n    \"selectedStudentName\" => \"BEHAVANA Gauffit\"\n    \"noteUserId\" => 109\n    \"noteParcourId\" => \"2\"\n    \"noteMatiereId\" => 3\n    \"noteTypeId\" => 3\n    \"noteValeur\" => 15\n    \"noteObservation\" => \"\"\n    \"noteActionMode\" => \"edit\"\n    \"selectedNoteId\" => 10219\n    \"notes\" => Illuminate\\Database\\Eloquent\\Collection {#2208\n      #items: array:55 [\n        0 => App\\Models\\Note {#1783\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10219\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 3\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 679\n            \"created_at\" => \"2023-09-07 16:39:25\"\n            \"updated_at\" => \"2023-09-07 16:39:25\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10219\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 3\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 679\n            \"created_at\" => \"2023-09-07 16:39:25\"\n            \"updated_at\" => \"2023-09-07 16:39:25\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1821 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1835 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Note {#1782\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10179\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 678\n            \"created_at\" => \"2023-09-07 16:39:23\"\n            \"updated_at\" => \"2023-09-07 16:39:23\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10179\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 678\n            \"created_at\" => \"2023-09-07 16:39:23\"\n            \"updated_at\" => \"2023-09-07 16:39:23\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1821 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1837 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Note {#1781\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10139\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 677\n            \"created_at\" => \"2023-09-07 16:39:02\"\n            \"updated_at\" => \"2023-09-07 16:39:02\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10139\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 677\n            \"created_at\" => \"2023-09-07 16:39:02\"\n            \"updated_at\" => \"2023-09-07 16:39:02\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1819 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1665 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Note {#1780\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10099\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 676\n            \"created_at\" => \"2023-09-07 16:39:01\"\n            \"updated_at\" => \"2023-09-07 16:39:01\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10099\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 676\n            \"created_at\" => \"2023-09-07 16:39:01\"\n            \"updated_at\" => \"2023-09-07 16:39:01\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1819 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1837 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Note {#1779\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Note {#1778\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Note {#1777\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Note {#1776\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Note {#1775\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        9 => App\\Models\\Note {#1774\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        10 => App\\Models\\Note {#1773\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        11 => App\\Models\\Note {#1772\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        12 => App\\Models\\Note {#1771\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        13 => App\\Models\\Note {#1770\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        14 => App\\Models\\Note {#1769\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        15 => App\\Models\\Note {#1768\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        16 => App\\Models\\Note {#1767\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        17 => App\\Models\\Note {#1766\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        18 => App\\Models\\Note {#1765\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        19 => App\\Models\\Note {#1764\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        20 => App\\Models\\Note {#1763\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        21 => App\\Models\\Note {#1762\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        22 => App\\Models\\Note {#1761\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        23 => App\\Models\\Note {#1760\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        24 => App\\Models\\Note {#1759\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        25 => App\\Models\\Note {#1758\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        26 => App\\Models\\Note {#1757\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        27 => App\\Models\\Note {#1756\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        28 => App\\Models\\Note {#1725\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        29 => App\\Models\\Note {#1726\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        30 => App\\Models\\Note {#1727\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        31 => App\\Models\\Note {#1728\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        32 => App\\Models\\Note {#1729\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        33 => App\\Models\\Note {#1730\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        34 => App\\Models\\Note {#1731\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        35 => App\\Models\\Note {#1732\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        36 => App\\Models\\Note {#1733\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        37 => App\\Models\\Note {#1734\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        38 => App\\Models\\Note {#1735\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        39 => App\\Models\\Note {#1736\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        40 => App\\Models\\Note {#1737\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        41 => App\\Models\\Note {#1738\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        42 => App\\Models\\Note {#1739\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        43 => App\\Models\\Note {#1740\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        44 => App\\Models\\Note {#1741\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        45 => App\\Models\\Note {#1742\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        46 => App\\Models\\Note {#1743\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        47 => App\\Models\\Note {#1744\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        48 => App\\Models\\Note {#1745\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        49 => App\\Models\\Note {#1746\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        50 => App\\Models\\Note {#1747\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        51 => App\\Models\\Note {#1748\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        52 => App\\Models\\Note {#1749\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        53 => App\\Models\\Note {#1750\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        54 => App\\Models\\Note {#1752\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"matieres\" => Illuminate\\Database\\Eloquent\\Collection {#2177\n      #items: array:9 [\n        0 => App\\Models\\Matiere {#706\n          #connection: \"mysql\"\n          #table: \"matieres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:7 [ …7]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Matiere {#1696\n          #connection: \"mysql\"\n          #table: \"matieres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:7 [ …7]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Matiere {#1935\n          #connection: \"mysql\"\n          #table: \"matieres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:7 [ …7]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Matiere {#1699\n          #connection: \"mysql\"\n          #table: \"matieres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:7 [ …7]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Matiere {#669\n          #connection: \"mysql\"\n          #table: \"matieres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:7 [ …7]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Matiere {#1933\n          #connection: \"mysql\"\n          #table: \"matieres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:7 [ …7]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Matiere {#1836\n          #connection: \"mysql\"\n          #table: \"matieres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:7 [ …7]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Matiere {#1850\n          #connection: \"mysql\"\n          #table: \"matieres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:7 [ …7]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Matiere {#1451\n          #connection: \"mysql\"\n          #table: \"matieres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:7 [ …7]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"noteTypes\" => Illuminate\\Database\\Eloquent\\Collection {#2188\n      #items: array:3 [\n        0 => App\\Models\\TypeNote {#1701\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [ …1]\n        }\n        1 => App\\Models\\TypeNote {#1848\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [ …1]\n        }\n        2 => App\\Models\\TypeNote {#1901\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [ …1]\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_semestre\" => App\\Models\\Semestre {#1905\n      #connection: \"mysql\"\n      #table: \"semestres\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"Semestre 1\"\n        \"niveau_id\" => 1\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"Semestre 1\"\n        \"niveau_id\" => 1\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"nom\"\n        1 => \"niveau_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1914\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1923\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_parcour\" => App\\Models\\Parcour {#1932\n      #connection: \"mysql\"\n      #table: \"parcours\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:3 [\n        0 => \"nom\"\n        1 => \"sigle\"\n        2 => \"mention_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"selectedSemestreId\" => \"\"\n    \"viewMode\" => \"table\"\n    \"searchQuery\" => \"\"\n    \"sortBy\" => \"created_at_desc\"\n    \"selectedNoteType\" => \"\"\n    \"autoSave\" => true\n    \"saveStatus\" => []\n    \"notesStatistics\" => array:5 [\n      \"average\" => 11.638\n      \"best\" => 18\n      \"worst\" => 0\n      \"count\" => 55\n      \"success_rate\" => 69.090909090909\n    ]\n    \"validationErrors\" => []\n    \"isValidating\" => false\n    \"page\" => \"70\"\n    \"paginators\" => array:1 [\n      \"page\" => \"70\"\n    ]\n  ]\n  \"name\" => \"etudiant\"\n  \"view\" => \"livewire.deraq.etudiant.notes\"\n  \"component\" => \"App\\Http\\Livewire\\Etudiant\"\n  \"id\" => \"JbDmk0syBswU4E5bsoUO\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestions/etudiants?page=70\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751977751\n]"}, "request": {"path_info": "/livewire/message/etudiant", "status_code": "<pre class=sf-dump id=sf-dump-1127054120 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1127054120\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1647394901 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1647394901\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1065757483 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">JbDmk0syBswU4E5bsoUO</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"18 characters\">gestions/etudiants</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">a25382a9</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:54</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>showCreateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEditModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showDeleteModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showRestoreModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>viewSupprimesMode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>selectedEtudiantId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedUserId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>etuName</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>newUser</span>\" => []\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>editParcours</span>\" => []\n      \"<span class=sf-dump-key>query</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreParcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>isLoading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>showFilters</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>compactView</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>bulkAction</span>\" => \"\"\n      \"<span class=sf-dump-key>totalEtudiants</span>\" => <span class=sf-dump-num>720</span>\n      \"<span class=sf-dump-key>totalSupprim&#233;s</span>\" => <span class=sf-dump-num>102</span>\n      \"<span class=sf-dump-key>selectedItems</span>\" => []\n      \"<span class=sf-dump-key>showNotes</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>selectedStudentName</span>\" => \"<span class=sf-dump-str title=\"16 characters\">BEHAVANA Gauffit</span>\"\n      \"<span class=sf-dump-key>noteUserId</span>\" => <span class=sf-dump-num>109</span>\n      \"<span class=sf-dump-key>noteParcourId</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>noteMatiereId</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>noteTypeId</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>noteValeur</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>noteObservation</span>\" => \"\"\n      \"<span class=sf-dump-key>noteActionMode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">edit</span>\"\n      \"<span class=sf-dump-key>selectedNoteId</span>\" => <span class=sf-dump-num>10219</span>\n      \"<span class=sf-dump-key>notes</span>\" => []\n      \"<span class=sf-dump-key>matieres</span>\" => []\n      \"<span class=sf-dump-key>noteTypes</span>\" => []\n      \"<span class=sf-dump-key>current_semestre</span>\" => []\n      \"<span class=sf-dump-key>current_niveau</span>\" => []\n      \"<span class=sf-dump-key>current_annee</span>\" => []\n      \"<span class=sf-dump-key>current_parcour</span>\" => []\n      \"<span class=sf-dump-key>selectedSemestreId</span>\" => \"\"\n      \"<span class=sf-dump-key>viewMode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">table</span>\"\n      \"<span class=sf-dump-key>searchQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>sortBy</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at_desc</span>\"\n      \"<span class=sf-dump-key>selectedNoteType</span>\" => \"\"\n      \"<span class=sf-dump-key>autoSave</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>saveStatus</span>\" => []\n      \"<span class=sf-dump-key>notesStatistics</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>average</span>\" => <span class=sf-dump-num>11.638</span>\n        \"<span class=sf-dump-key>best</span>\" => <span class=sf-dump-num>18</span>\n        \"<span class=sf-dump-key>worst</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>55</span>\n        \"<span class=sf-dump-key>success_rate</span>\" => <span class=sf-dump-num>69.090909090909</span>\n      </samp>]\n      \"<span class=sf-dump-key>validationErrors</span>\" => []\n      \"<span class=sf-dump-key>isValidating</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Note</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:55</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>10179</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>10139</span>\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-num>10099</span>\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-num>10059</span>\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-num>10019</span>\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-num>9979</span>\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-num>9939</span>\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-num>9899</span>\n            <span class=sf-dump-index>9</span> => <span class=sf-dump-num>9859</span>\n            <span class=sf-dump-index>10</span> => <span class=sf-dump-num>9819</span>\n            <span class=sf-dump-index>11</span> => <span class=sf-dump-num>9779</span>\n            <span class=sf-dump-index>12</span> => <span class=sf-dump-num>9739</span>\n            <span class=sf-dump-index>13</span> => <span class=sf-dump-num>9699</span>\n            <span class=sf-dump-index>14</span> => <span class=sf-dump-num>9659</span>\n            <span class=sf-dump-index>15</span> => <span class=sf-dump-num>9619</span>\n            <span class=sf-dump-index>16</span> => <span class=sf-dump-num>8520</span>\n            <span class=sf-dump-index>17</span> => <span class=sf-dump-num>8480</span>\n            <span class=sf-dump-index>18</span> => <span class=sf-dump-num>8415</span>\n            <span class=sf-dump-index>19</span> => <span class=sf-dump-num>8356</span>\n            <span class=sf-dump-index>20</span> => <span class=sf-dump-num>8302</span>\n            <span class=sf-dump-index>21</span> => <span class=sf-dump-num>8262</span>\n            <span class=sf-dump-index>22</span> => <span class=sf-dump-num>8012</span>\n            <span class=sf-dump-index>23</span> => <span class=sf-dump-num>7874</span>\n            <span class=sf-dump-index>24</span> => <span class=sf-dump-num>7834</span>\n            <span class=sf-dump-index>25</span> => <span class=sf-dump-num>4575</span>\n            <span class=sf-dump-index>26</span> => <span class=sf-dump-num>4518</span>\n            <span class=sf-dump-index>27</span> => <span class=sf-dump-num>4461</span>\n            <span class=sf-dump-index>28</span> => <span class=sf-dump-num>4368</span>\n            <span class=sf-dump-index>29</span> => <span class=sf-dump-num>4311</span>\n            <span class=sf-dump-index>30</span> => <span class=sf-dump-num>4180</span>\n            <span class=sf-dump-index>31</span> => <span class=sf-dump-num>4123</span>\n            <span class=sf-dump-index>32</span> => <span class=sf-dump-num>3453</span>\n            <span class=sf-dump-index>33</span> => <span class=sf-dump-num>3380</span>\n            <span class=sf-dump-index>34</span> => <span class=sf-dump-num>3323</span>\n            <span class=sf-dump-index>35</span> => <span class=sf-dump-num>3266</span>\n            <span class=sf-dump-index>36</span> => <span class=sf-dump-num>2734</span>\n            <span class=sf-dump-index>37</span> => <span class=sf-dump-num>2677</span>\n            <span class=sf-dump-index>38</span> => <span class=sf-dump-num>2324</span>\n            <span class=sf-dump-index>39</span> => <span class=sf-dump-num>1625</span>\n            <span class=sf-dump-index>40</span> => <span class=sf-dump-num>1568</span>\n            <span class=sf-dump-index>41</span> => <span class=sf-dump-num>1511</span>\n            <span class=sf-dump-index>42</span> => <span class=sf-dump-num>1454</span>\n            <span class=sf-dump-index>43</span> => <span class=sf-dump-num>1397</span>\n            <span class=sf-dump-index>44</span> => <span class=sf-dump-num>1295</span>\n            <span class=sf-dump-index>45</span> => <span class=sf-dump-num>1226</span>\n            <span class=sf-dump-index>46</span> => <span class=sf-dump-num>1163</span>\n            <span class=sf-dump-index>47</span> => <span class=sf-dump-num>1106</span>\n            <span class=sf-dump-index>48</span> => <span class=sf-dump-num>1049</span>\n            <span class=sf-dump-index>49</span> => <span class=sf-dump-num>992</span>\n            <span class=sf-dump-index>50</span> => <span class=sf-dump-num>885</span>\n            <span class=sf-dump-index>51</span> => <span class=sf-dump-num>828</span>\n            <span class=sf-dump-index>52</span> => <span class=sf-dump-num>771</span>\n            <span class=sf-dump-index>53</span> => <span class=sf-dump-num>714</span>\n            <span class=sf-dump-index>54</span> => <span class=sf-dump-num>657</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">matiere</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">typeNote</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>matieres</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Matiere</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>33</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>34</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>35</span>\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-num>36</span>\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-num>37</span>\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-num>38</span>\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-num>41</span>\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-num>42</span>\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-num>43</span>\n            <span class=sf-dump-index>9</span> => <span class=sf-dump-num>45</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>noteTypes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\TypeNote</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>2</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>3</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>models</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>current_semestre</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Semestre</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Niveau</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\AnneeUniversitaire</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_parcour</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Parcour</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">bd10ebd96c570d253fbf74d3218dd15931efc27c9ee0e516741a23cff7ccc841</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ycdgh</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">noteParcourId</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str>2</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">e9ys</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">updateParcour</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>2</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065757483\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1452842587 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2793</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFRc0xvVnFuQjM5QVJycU9uUVBqQUE9PSIsInZhbHVlIjoiOHBuVW14a29qaWtTckZScHdYMzNGUXJJek80TFpkMnEySHIweEgyMXdyMzQwVlc1KzU1aGVMK0RtOTA4b0Z0OFVMcWZBb0xLSVovUWtxaVlMd2ZUTjVZNDJzSStNcVEwSHNYNTl1d01zSVdTU2V0T01hUnpDRTM4WXMxVm9YOHciLCJtYWMiOiJkZGFmYmM1ZTViNDRkOGUyN2JmYTNkY2Q0NzE2NmY4N2M5YjljOWRlYjI0MGZiOWFmMzRjYTU0YzZkMGY0MzU5IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImVjSlBmMDNVVVNLblVwT1h1NXExM0E9PSIsInZhbHVlIjoiUEJFTDlLdnB4T0RDVEtNdXZVQitmUGw5azhqWDZGV3Q5UEx1Y3d4Mk9LVGNQL2pyY2xmRjFWaWhMYTJNNGtoN3o3NHdjZDNwSm1yQ2U0cVBlY0tYc3c1QVoyTy9hMXREYWdtSDRoR3J0STZ2bmdtSnZvNG5id1FGV29uaytlZWwiLCJtYWMiOiJjMTc3NmMwOGE5YjBkNTk3ZDAzZjBlNmY4ODg5M2NlNzc3MWRhNjkwZmJmN2I1NDFiMjFkNDQ0MjUwYzg5YWQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1452842587\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1639567598 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56222</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2793</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2793</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFRc0xvVnFuQjM5QVJycU9uUVBqQUE9PSIsInZhbHVlIjoiOHBuVW14a29qaWtTckZScHdYMzNGUXJJek80TFpkMnEySHIweEgyMXdyMzQwVlc1KzU1aGVMK0RtOTA4b0Z0OFVMcWZBb0xLSVovUWtxaVlMd2ZUTjVZNDJzSStNcVEwSHNYNTl1d01zSVdTU2V0T01hUnpDRTM4WXMxVm9YOHciLCJtYWMiOiJkZGFmYmM1ZTViNDRkOGUyN2JmYTNkY2Q0NzE2NmY4N2M5YjljOWRlYjI0MGZiOWFmMzRjYTU0YzZkMGY0MzU5IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImVjSlBmMDNVVVNLblVwT1h1NXExM0E9PSIsInZhbHVlIjoiUEJFTDlLdnB4T0RDVEtNdXZVQitmUGw5azhqWDZGV3Q5UEx1Y3d4Mk9LVGNQL2pyY2xmRjFWaWhMYTJNNGtoN3o3NHdjZDNwSm1yQ2U0cVBlY0tYc3c1QVoyTy9hMXREYWdtSDRoR3J0STZ2bmdtSnZvNG5id1FGV29uaytlZWwiLCJtYWMiOiJjMTc3NmMwOGE5YjBkNTk3ZDAzZjBlNmY4ODg5M2NlNzc3MWRhNjkwZmJmN2I1NDFiMjFkNDQ0MjUwYzg5YWQzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751983264.8767</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751983264</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639567598\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-782046729 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IC6ylzyOLsmp8eqEPKRtYbNYwxNcQ3yBFxnhfM9e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782046729\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1843729586 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 14:01:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IndiMFpDejlBS2l4cXVWNHZPRlRkN1E9PSIsInZhbHVlIjoiV2J0aHUweDFHZVVoWVNSV3JTdndBN3g0RzdUOWJ3Y3c2WFY1djZ1WFRmYkJFcHYxd2JaWElvV2l3NVJqWjZuVUV2eFFRNDRkcW0rV1FYUU5teGRTWkpPZHlVMHhZckZIbVRPcUhEVFVlSjhUWlZ2dllhdElxWWx1WDllMUtvcm8iLCJtYWMiOiJiNjQ5ODUwNDZjNTlkNTU1ODU1NzY0M2Q3MWIwNGRkZjc4YWQzMWUxMjhhNmM1ZWY3OGNkYmY3OWYwYzdkNGI4IiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 16:01:07 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IkVFeGFjcW5LTWE3dlZuN24vRnRPOFE9PSIsInZhbHVlIjoiRDB5SUs0RUtUT3A3MzdOb0lkdjkxdVduYkVRQXZlZ0R0S2ZTUkxmMC9iSXhISVViVGNzeXI1eDNvT1l3WjJYc1hQc3puV0ZuOTh2Q1RzY3Z4UEp1Uk9CVDBEVVdiRmJSNHVqLzgyNlpSYjdqOEhtaDl1TEJNU21JeVA3TGtRZTQiLCJtYWMiOiJiOTNkZjI4ZDAzYTVmNjJkOTZmNjIyNDk1MWEyZmI4YjgxNGRkOWJhYjFkMzcwZmE3NWI2MTVlYzEzMTZhODc4IiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 16:01:07 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IndiMFpDejlBS2l4cXVWNHZPRlRkN1E9PSIsInZhbHVlIjoiV2J0aHUweDFHZVVoWVNSV3JTdndBN3g0RzdUOWJ3Y3c2WFY1djZ1WFRmYkJFcHYxd2JaWElvV2l3NVJqWjZuVUV2eFFRNDRkcW0rV1FYUU5teGRTWkpPZHlVMHhZckZIbVRPcUhEVFVlSjhUWlZ2dllhdElxWWx1WDllMUtvcm8iLCJtYWMiOiJiNjQ5ODUwNDZjNTlkNTU1ODU1NzY0M2Q3MWIwNGRkZjc4YWQzMWUxMjhhNmM1ZWY3OGNkYmY3OWYwYzdkNGI4IiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 16:01:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IkVFeGFjcW5LTWE3dlZuN24vRnRPOFE9PSIsInZhbHVlIjoiRDB5SUs0RUtUT3A3MzdOb0lkdjkxdVduYkVRQXZlZ0R0S2ZTUkxmMC9iSXhISVViVGNzeXI1eDNvT1l3WjJYc1hQc3puV0ZuOTh2Q1RzY3Z4UEp1Uk9CVDBEVVdiRmJSNHVqLzgyNlpSYjdqOEhtaDl1TEJNU21JeVA3TGtRZTQiLCJtYWMiOiJiOTNkZjI4ZDAzYTVmNjJkOTZmNjIyNDk1MWEyZmI4YjgxNGRkOWJhYjFkMzcwZmE3NWI2MTVlYzEzMTZhODc4IiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 16:01:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843729586\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1055263830 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751977751</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055263830\", {\"maxDepth\":0})</script>\n"}}
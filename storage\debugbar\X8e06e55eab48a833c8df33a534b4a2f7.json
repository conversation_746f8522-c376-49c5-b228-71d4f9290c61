{"__meta": {"id": "X8e06e55eab48a833c8df33a534b4a2f7", "datetime": "2025-07-08 17:17:50", "utime": 1751984270.957189, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751984269.766229, "end": 1751984270.957246, "duration": 1.1910171508789062, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": 1751984269.766229, "relative_start": 0, "end": 1751984270.312629, "relative_end": 1751984270.312629, "duration": 0.5464000701904297, "duration_str": "546ms", "params": [], "collector": null}, {"label": "Application", "start": 1751984270.314001, "relative_start": 0.5477721691131592, "end": 1751984270.957252, "relative_end": 5.9604644775390625e-06, "duration": 0.6432509422302246, "duration_str": "643ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 23324672, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login (\\resources\\views\\auth\\login.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/auth/login.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 3, "params": ["__env", "app", "errors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=19\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:19-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DWRAws4N4coU0gqyvlobSFgdRBqPUeeBDLmtnP4Q", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-2029802218 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2029802218\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1923757561 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1923757561\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1896843209 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1896843209\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-535791158 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"721 characters\">XSRF-TOKEN=eyJpdiI6Im03SXgrUjRYVjMrd1EybmhUeXNNM0E9PSIsInZhbHVlIjoiNUVSNWxsNC9UWTBoVFNJSUYyK1IwL3g5RnR2SXFiRjIzZzY5Z0JTUTEreStsdkh0L3BxUjZCMjZaZHFoVUdBMy9QNnorWmdnMkRFRWtrNGlMVVZObWF4MUVYT1BPYXp1SStJYm5WN0JydTFWbkt5UzhLakYweFQyNW5UUlIzVlIiLCJtYWMiOiJiOTFlOWE3MWRiMzgwYzkwYjNiNTA2N2RlZTIwMTliYzIzMzVhNzAyZjY2YjY2MTY5MWQ2ZGFiMGNlMmU0MDBiIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Ilk4d3dkczhjL21EWHdmNVBwbS9KN0E9PSIsInZhbHVlIjoiT0svM2hZcnBRdWpDdlBHYUUxVXNVYXdxM0p5aFdQaGF0NG9pdmFzaUpXRnloSUNyOWJDd3BVajlXY1lBYThpMlFWL3RDTkU4K3JKNlg2U2pydmVCRy9OeFFXWkZ3S3lDYlJBM3ZtZkU0eGZVTGsvYXIrY04zcHRhWlNIcDMvcWsiLCJtYWMiOiI5Yzg0MzA2MDQ3YTJhN2Q1ZmZiZTZiYWFiMjExM2RhZWYxMjMxZDkyYTI4YmU4NGVjOGYyODM5YTA0ODZlZjUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535791158\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1063600972 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57090</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"721 characters\">XSRF-TOKEN=eyJpdiI6Im03SXgrUjRYVjMrd1EybmhUeXNNM0E9PSIsInZhbHVlIjoiNUVSNWxsNC9UWTBoVFNJSUYyK1IwL3g5RnR2SXFiRjIzZzY5Z0JTUTEreStsdkh0L3BxUjZCMjZaZHFoVUdBMy9QNnorWmdnMkRFRWtrNGlMVVZObWF4MUVYT1BPYXp1SStJYm5WN0JydTFWbkt5UzhLakYweFQyNW5UUlIzVlIiLCJtYWMiOiJiOTFlOWE3MWRiMzgwYzkwYjNiNTA2N2RlZTIwMTliYzIzMzVhNzAyZjY2YjY2MTY5MWQ2ZGFiMGNlMmU0MDBiIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Ilk4d3dkczhjL21EWHdmNVBwbS9KN0E9PSIsInZhbHVlIjoiT0svM2hZcnBRdWpDdlBHYUUxVXNVYXdxM0p5aFdQaGF0NG9pdmFzaUpXRnloSUNyOWJDd3BVajlXY1lBYThpMlFWL3RDTkU4K3JKNlg2U2pydmVCRy9OeFFXWkZ3S3lDYlJBM3ZtZkU0eGZVTGsvYXIrY04zcHRhWlNIcDMvcWsiLCJtYWMiOiI5Yzg0MzA2MDQ3YTJhN2Q1ZmZiZTZiYWFiMjExM2RhZWYxMjMxZDkyYTI4YmU4NGVjOGYyODM5YTA0ODZlZjUxIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751984269.7662</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751984269</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063600972\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1361469196 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DWRAws4N4coU0gqyvlobSFgdRBqPUeeBDLmtnP4Q</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BOc5Sq00jg6kwOdOW7Ev4tnOM7qTYFsgcA7XhKAP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1361469196\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-337258505 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 14:17:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkFzUVJtOG5xNC9CUWRsNkRJMSttTkE9PSIsInZhbHVlIjoiOExGZ21qZzV0TkovVzdBc0JoT0c3Z0o2YUtGVXVMNEpHYjFaZGc4MFgvdjhRVkN5T1pyVVdQR3JFVGxna3BjQnlKSmFwbWxzaGwzU0JVZUdOTVNCOHZPcGVQYWtFcjVDc2xCdnhXbHlEZTlLQk9QNmxZcUhSazhxUzFTMG1jWVkiLCJtYWMiOiJjZjE3YWI2NDJmNGNkYTc5NTZkZDU5NTI0N2JjMzliOTI2YzU4OWU0ZjlkZDQyYjg3YWQ5MGEyODE4MzFlNjZhIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 16:17:50 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IksvRGxjd3lsUUE3bVlxOHlZVGRrYVE9PSIsInZhbHVlIjoibXN5T1YvejFpd21VSFJ5eWNnL3IzYVZnYmZHTUsweGtlbnlmK2pkOTU1NmNmREM4QUxhMkRIQVEwOVFVeksrUThnWXh4U0srNlBKYW9KVUcxamtvU01DMGlzalgvUkk1aFNsOHhTVStwc1gya3dteUtXTzN0UHErY3lJOGN4UzgiLCJtYWMiOiIyNTNkYmZlMzY2MzFhM2NmMjllZWQ4YWFkNjMxOGEwMjRmMmQyNDM0NWQ1N2I4ZTVkNDI0MGVkYTQyODkzZWY2IiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 16:17:50 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkFzUVJtOG5xNC9CUWRsNkRJMSttTkE9PSIsInZhbHVlIjoiOExGZ21qZzV0TkovVzdBc0JoT0c3Z0o2YUtGVXVMNEpHYjFaZGc4MFgvdjhRVkN5T1pyVVdQR3JFVGxna3BjQnlKSmFwbWxzaGwzU0JVZUdOTVNCOHZPcGVQYWtFcjVDc2xCdnhXbHlEZTlLQk9QNmxZcUhSazhxUzFTMG1jWVkiLCJtYWMiOiJjZjE3YWI2NDJmNGNkYTc5NTZkZDU5NTI0N2JjMzliOTI2YzU4OWU0ZjlkZDQyYjg3YWQ5MGEyODE4MzFlNjZhIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 16:17:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IksvRGxjd3lsUUE3bVlxOHlZVGRrYVE9PSIsInZhbHVlIjoibXN5T1YvejFpd21VSFJ5eWNnL3IzYVZnYmZHTUsweGtlbnlmK2pkOTU1NmNmREM4QUxhMkRIQVEwOVFVeksrUThnWXh4U0srNlBKYW9KVUcxamtvU01DMGlzalgvUkk1aFNsOHhTVStwc1gya3dteUtXTzN0UHErY3lJOGN4UzgiLCJtYWMiOiIyNTNkYmZlMzY2MzFhM2NmMjllZWQ4YWFkNjMxOGEwMjRmMmQyNDM0NWQ1N2I4ZTVkNDI0MGVkYTQyODkzZWY2IiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 16:17:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-337258505\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-59714015 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DWRAws4N4coU0gqyvlobSFgdRBqPUeeBDLmtnP4Q</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59714015\", {\"maxDepth\":0})</script>\n"}}
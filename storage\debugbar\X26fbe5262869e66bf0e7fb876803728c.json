{"__meta": {"id": "X26fbe5262869e66bf0e7fb876803728c", "datetime": "2025-07-08 17:18:10", "utime": 1751984290.06628, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751984288.378806, "end": 1751984290.066314, "duration": 1.6875078678131104, "duration_str": "1.69s", "measures": [{"label": "Booting", "start": 1751984288.378806, "relative_start": 0, "end": 1751984289.554483, "relative_end": 1751984289.554483, "duration": 1.1756768226623535, "duration_str": "1.18s", "params": [], "collector": null}, {"label": "Application", "start": 1751984289.556968, "relative_start": 1.178161859512329, "end": 1751984290.066317, "relative_end": 3.0994415283203125e-06, "duration": 0.5093491077423096, "duration_str": "509ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 23325152, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login (\\resources\\views\\auth\\login.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/auth/login.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 3, "params": ["__env", "app", "errors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=19\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:19-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7irXq9wtJbT3pXo7VhIsI9RVG1dIn1knYSLZzJlk", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/gestions/etudiants?page=70\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-911923813 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-911923813\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1144218578 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1144218578\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-556357606 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-556357606\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1443375937 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/habilitations/utilisateurs?page=4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"721 characters\">XSRF-TOKEN=eyJpdiI6ImtRNVI4TkdWRlJ6dFR0TU1zV0RxZ0E9PSIsInZhbHVlIjoiK3RralNzUFY1WlpCc3F1Z1p0OGpKTWhuVXZXVXZkWFlwK3htTzJHWjhXVlk5MU5qVEZsYXd5MHpmeURvTHE1ekp0Z2dqSnhMTHcrT0FQei9wRlNGVm1MZUNabkt2TnkyWDIxUzJ5Ynh2TFlETlhFaTB6Zkp6ZTZUZHNWTTZ0cWsiLCJtYWMiOiI5YzBjYjViM2NjMDI0OWNiNWExNTIxNzA1N2I0YjgwY2QwY2NhMzVlZmIwNzk2Mjk0YmNhMDBjMjRlNjdhMDRjIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjM2eEN1cU1jVXdVWFFWajJ5OUQ4SGc9PSIsInZhbHVlIjoiUEZwOHdPdlVOWUtiSW5aNE9UdThSWU9ZUFJiNzc3RDVWN1V6SzRObTRXRzJaZzJkQ3RocXhKVkY5NHRkVUVOdUcvUURTZ2t6VlM1Wlh1RC95RG5PV3lWYlhRYnpSWTlPYkl5ckpjdEEyZGIxcG1jcGorWVVIbDBjcTBrQmkzc0UiLCJtYWMiOiIzYzJiZDc3ODdkMDliYTYyMTY5MWRhMmFiYzA0NjgxNGM0ZTk2ZTQ0ODU2OGYzNDlkZGFkZjI0ZDIzZjIyNzhhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1443375937\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-659680334 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57136</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/habilitations/utilisateurs?page=4</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"721 characters\">XSRF-TOKEN=eyJpdiI6ImtRNVI4TkdWRlJ6dFR0TU1zV0RxZ0E9PSIsInZhbHVlIjoiK3RralNzUFY1WlpCc3F1Z1p0OGpKTWhuVXZXVXZkWFlwK3htTzJHWjhXVlk5MU5qVEZsYXd5MHpmeURvTHE1ekp0Z2dqSnhMTHcrT0FQei9wRlNGVm1MZUNabkt2TnkyWDIxUzJ5Ynh2TFlETlhFaTB6Zkp6ZTZUZHNWTTZ0cWsiLCJtYWMiOiI5YzBjYjViM2NjMDI0OWNiNWExNTIxNzA1N2I0YjgwY2QwY2NhMzVlZmIwNzk2Mjk0YmNhMDBjMjRlNjdhMDRjIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjM2eEN1cU1jVXdVWFFWajJ5OUQ4SGc9PSIsInZhbHVlIjoiUEZwOHdPdlVOWUtiSW5aNE9UdThSWU9ZUFJiNzc3RDVWN1V6SzRObTRXRzJaZzJkQ3RocXhKVkY5NHRkVUVOdUcvUURTZ2t6VlM1Wlh1RC95RG5PV3lWYlhRYnpSWTlPYkl5ckpjdEEyZGIxcG1jcGorWVVIbDBjcTBrQmkzc0UiLCJtYWMiOiIzYzJiZDc3ODdkMDliYTYyMTY5MWRhMmFiYzA0NjgxNGM0ZTk2ZTQ0ODU2OGYzNDlkZGFkZjI0ZDIzZjIyNzhhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751984288.3788</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751984288</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659680334\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1312297159 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7irXq9wtJbT3pXo7VhIsI9RVG1dIn1knYSLZzJlk</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nL29mUNQMtbtZg6fzu1W7x95KDVa95Q0CXjhMa7W</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1312297159\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-48760394 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 14:18:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjhsSkRHSTl5bWhQNDVFZnNaK2E5c3c9PSIsInZhbHVlIjoiYytsZXZXSTJsK3dXdzhNandtNUJXQzNTK3FlRkEzYWdhemdxcHN2ZExveDJpc0NwZHBjc09WWm0wVlozQ2xJcnhQRzJRSXlENE4rYlVmN2U3ZDVya1R6SjFKYXAyeTYzK1JwUWs4TTRJNlRnWFdrUnJuTjBqaTZKazcxZXFwakIiLCJtYWMiOiJkMzRjMjYxNTJkNGFlNjAzODg5MjRlNzJjZDVlZWRiN2QwNzRhNGI3NjkzZWJlOTg2MmNlZDMyMDAwNmY3ZjViIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 16:18:09 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IlpzdXp0VDlEblVhYW5lc3RqTURQVWc9PSIsInZhbHVlIjoiUDV6S0NNTE4vcXc4ekVoV1R4TWNLbDU0eGF3dG5OdFBqUHFBZmVCWmVRYTk0eFRlNXFPMHJTQk85S3A2SDBiSjdsTDRQbERYSkhpWFdpcDdPc0VWa0NwcjB1Y0J3ZVB6eitMdUJJc1E1QkRjNjg5dFZ3OXdxQzBuZXBVZ2V5UkgiLCJtYWMiOiJkN2M3ZmM2MWJmYjkyOTQwNmUxY2JhOTA1YzZkMTZjYjhmNGJiODkxMTg5YWM0ZDYwMGY0YmU5NzExZDc0ZmJkIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 16:18:09 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjhsSkRHSTl5bWhQNDVFZnNaK2E5c3c9PSIsInZhbHVlIjoiYytsZXZXSTJsK3dXdzhNandtNUJXQzNTK3FlRkEzYWdhemdxcHN2ZExveDJpc0NwZHBjc09WWm0wVlozQ2xJcnhQRzJRSXlENE4rYlVmN2U3ZDVya1R6SjFKYXAyeTYzK1JwUWs4TTRJNlRnWFdrUnJuTjBqaTZKazcxZXFwakIiLCJtYWMiOiJkMzRjMjYxNTJkNGFlNjAzODg5MjRlNzJjZDVlZWRiN2QwNzRhNGI3NjkzZWJlOTg2MmNlZDMyMDAwNmY3ZjViIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 16:18:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IlpzdXp0VDlEblVhYW5lc3RqTURQVWc9PSIsInZhbHVlIjoiUDV6S0NNTE4vcXc4ekVoV1R4TWNLbDU0eGF3dG5OdFBqUHFBZmVCWmVRYTk0eFRlNXFPMHJTQk85S3A2SDBiSjdsTDRQbERYSkhpWFdpcDdPc0VWa0NwcjB1Y0J3ZVB6eitMdUJJc1E1QkRjNjg5dFZ3OXdxQzBuZXBVZ2V5UkgiLCJtYWMiOiJkN2M3ZmM2MWJmYjkyOTQwNmUxY2JhOTA1YzZkMTZjYjhmNGJiODkxMTg5YWM0ZDYwMGY0YmU5NzExZDc0ZmJkIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 16:18:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48760394\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-189034435 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7irXq9wtJbT3pXo7VhIsI9RVG1dIn1knYSLZzJlk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-189034435\", {\"maxDepth\":0})</script>\n"}}
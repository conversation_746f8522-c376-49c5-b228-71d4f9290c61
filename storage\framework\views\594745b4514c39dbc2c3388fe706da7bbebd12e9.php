<!-- Edit Note Modal - Livewire Only -->
<?php if($showEditNoteModal): ?>
<div class="modal-backdrop fade show" style="z-index: 1040;"></div>
<div class="modal fade show" style="display: block; z-index: 1050;" tabindex="-1" wire:ignore.self>
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifier la Note</h5>
                <button type="button" class="btn-close" wire:click="closeEditNoteModal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if(!empty($editNote)): ?>
                    <form wire:submit.prevent="updateNote">
                        
                        <div class="mb-3">
                            <label class="form-label">Étudiant</label>
                            <input type="text" class="form-control" value="<?php echo e($editNote['student_name'] ?? 'N/A'); ?>" readonly disabled>
                        </div>

                        <div class="mb-3">
                            <label for="editNote.matiere_id" class="form-label">Matière <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['editNote.matiere_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" wire:model.defer="editNote.matiere_id" required>
                                <option value="">Sélectionner...</option>
                                <?php if(!empty($editNote['available_matieres'])): ?>
                                    <?php $__currentLoopData = $editNote['available_matieres']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $matiere): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($matiere['id']); ?>"><?php echo e($matiere['nom']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                     
                                     <option value="" disabled>Chargement des matières...</option>
                                <?php endif; ?>
                            </select>
                            <?php $__errorArgs = ['editNote.matiere_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="editNote.type_note_id" class="form-label">Type d'évaluation <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['editNote.type_note_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" wire:model.defer="editNote.type_note_id" required>
                                <option value="">Sélectionner...</option>
                                <?php if(!empty($editNote['available_types'])): ?>
                                    <?php $__currentLoopData = $editNote['available_types']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($type['id']); ?>"><?php echo e($type['nom']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                     <option value="" disabled>Chargement des types...</option>
                                <?php endif; ?>
                            </select>
                            <?php $__errorArgs = ['editNote.type_note_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="editNote.valeur" class="form-label">Note (sur 20) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control <?php $__errorArgs = ['editNote.valeur'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" wire:model.defer="editNote.valeur" min="0" max="20" step="0.01" required>
                            <?php $__errorArgs = ['editNote.valeur'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="editNote.observation" class="form-label">Observation</label>
                            <textarea class="form-control" wire:model.defer="editNote.observation" rows="3" placeholder="Observation optionnelle..."></textarea>
                        </div>

                        <div class="modal-footer px-0 pb-0">
                            <button type="button" class="btn btn-alt-secondary" wire:click="closeEditNoteModal">Annuler</button>
                            <button type="submit" class="btn btn-primary">
                                <span wire:loading wire:target="updateNote" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                <?php else: ?>
                    
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/deraq/etudiant/modals/edit-note-modal.blade.php ENDPATH**/ ?>
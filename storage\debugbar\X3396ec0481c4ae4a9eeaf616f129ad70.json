{"__meta": {"id": "X3396ec0481c4ae4a9eeaf616f129ad70", "datetime": "2025-07-08 16:44:14", "utime": **********.271107, "method": "POST", "uri": "/livewire/message/etudiant", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 5, "messages": [{"message": "[16:44:14] LOG.info: Detected N+1 Query", "message_html": null, "is_string": false, "label": "info", "time": **********.254442, "collector": "log"}, {"message": "[16:44:14] LOG.info: Model: App\\Models\\Note\r\nRelation: App\\Models\\Matiere\r\nNum-Called: 32\r\nCall-Stack:\r\n#20 \\app\\Http\\Livewire\\Etudiant.php:1002\r\n#21 \\app\\Http\\Livewire\\Etudiant.php:855\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#25 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#26 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#27 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#28 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#29 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#30 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.267652, "collector": "log"}, {"message": "[16:44:14] LOG.info: Model: App\\Models\\Matiere\r\nRelation: App\\Models\\Ue\r\nNum-Called: 32\r\nCall-Stack:\r\n#25 \\app\\Http\\Livewire\\Etudiant.php:1002\r\n#26 \\app\\Http\\Livewire\\Etudiant.php:855\r\n#27 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#28 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#31 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#32 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#33 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#34 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#35 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.268026, "collector": "log"}, {"message": "[16:44:14] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Parcour\r\nNum-Called: 32\r\nCall-Stack:\r\n#30 \\app\\Http\\Livewire\\Etudiant.php:1002\r\n#31 \\app\\Http\\Livewire\\Etudiant.php:855\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#36 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#37 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#38 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#39 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#40 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.268312, "collector": "log"}, {"message": "[16:44:14] LOG.info: Model: App\\Models\\Note\r\nRelation: App\\Models\\TypeNote\r\nNum-Called: 32\r\nCall-Stack:\r\n#20 \\app\\Http\\Livewire\\Etudiant.php:1002\r\n#21 \\app\\Http\\Livewire\\Etudiant.php:855\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#25 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#26 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#27 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#28 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#29 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#30 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.268713, "collector": "log"}]}, "time": {"start": 1751982230.755653, "end": **********.27123, "duration": 23.5155770778656, "duration_str": "23.52s", "measures": [{"label": "Booting", "start": 1751982230.755653, "relative_start": 0, "end": 1751982232.390686, "relative_end": 1751982232.390686, "duration": 1.635033130645752, "duration_str": "1.64s", "params": [], "collector": null}, {"label": "Application", "start": 1751982232.391681, "relative_start": 1.6360280513763428, "end": **********.271233, "relative_end": 3.0994415283203125e-06, "duration": 21.879552125930786, "duration_str": "21.88s", "params": [], "collector": null}]}, "memory": {"peak_usage": 29157256, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "livewire.deraq.etudiant.notes (\\resources\\views\\livewire\\deraq\\etudiant\\notes.blade.php)", "param_count": 61, "params": ["parcours", "semestres", "livewireLayout", "errors", "_instance", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "showEditNoteModal", "editNote", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/etudiant/notes.blade.php&line=0"}, {"name": "components.note-form-modal (\\resources\\views\\components\\note-form-modal.blade.php)", "param_count": 71, "params": ["__env", "app", "errors", "_instance", "parcours", "semestres", "livewireLayout", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "showEditNoteModal", "editNote", "page", "paginators", "__currentLoopData", "sem", "loop", "type", "__empty_1", "note", "index", "noteClass"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/note-form-modal.blade.php&line=0"}, {"name": "livewire.deraq.etudiant.modals.edit-note-modal (\\resources\\views\\livewire\\deraq\\etudiant\\modals\\edit-note-modal.blade.php)", "param_count": 71, "params": ["__env", "app", "errors", "_instance", "parcours", "semestres", "livewireLayout", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "showEditNoteModal", "editNote", "page", "paginators", "__currentLoopData", "sem", "loop", "type", "__empty_1", "note", "index", "noteClass"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/etudiant/modals/edit-note-modal.blade.php&line=0"}, {"name": "components.toast-notifications (\\resources\\views\\components\\toast-notifications.blade.php)", "param_count": 71, "params": ["__env", "app", "errors", "_instance", "parcours", "semestres", "livewireLayout", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "showEditNoteModal", "editNote", "page", "paginators", "__currentLoopData", "sem", "loop", "type", "__empty_1", "note", "index", "noteClass"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/toast-notifications.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 240, "nb_failed_statements": 0, "accumulated_duration": 18.681380000000004, "accumulated_duration_str": "18.68s", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.17252, "duration_str": "173ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 0.923}, {"sql": "select * from `notes` where `notes`.`id` in (10219, 10179, 10139, 10099, 10059, 10019, 9979, 9939, 9899, 9859, 9819, 9779, 9739, 9699, 9659, 9619, 8520, 8480, 8415, 8356, 8302, 8262, 8012, 7874, 7834, 4575, 4518, 4461, 4368, 4311, 4180, 4123, 3453, 3380, 3323, 3266, 2734, 2677, 2324, 1625, 1568, 1511, 1454, 1397, 1295, 1226, 1163, 1106, 1049, 992, 885, 828, 771, 714, 657)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.03853, "duration_str": "38.53ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 0.923, "width_percent": 0.206}, {"sql": "select * from `matieres` where `matieres`.`id` in (33, 34, 35, 36, 37, 38, 41, 42, 43, 45, 196, 197, 198, 199, 200, 201, 202, 203) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 1.13, "width_percent": 0.006}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 1.136, "width_percent": 0.005}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.08829000000000001, "duration_str": "88.29ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 1.14, "width_percent": 0.473}, {"sql": "select * from `semestres` where `semestres`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.12776, "duration_str": "128ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 1.613, "width_percent": 0.684}, {"sql": "select * from `niveaux` where `niveaux`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0792, "duration_str": "79.2ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 2.297, "width_percent": 0.424}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.16996, "duration_str": "170ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 2.721, "width_percent": 0.91}, {"sql": "select * from `parcours` where `parcours`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.10107, "duration_str": "101ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 3.631, "width_percent": 0.541}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09489, "duration_str": "94.89ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 4.172, "width_percent": 0.508}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07103, "duration_str": "71.03ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 4.679, "width_percent": 0.38}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.18891, "duration_str": "189ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 5.06, "width_percent": 1.011}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.11393, "duration_str": "114ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 6.071, "width_percent": 0.61}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.17941, "duration_str": "179ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 6.681, "width_percent": 0.96}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05781, "duration_str": "57.81ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 7.641, "width_percent": 0.309}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03293, "duration_str": "32.93ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 7.951, "width_percent": 0.176}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08072, "duration_str": "80.72ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 8.127, "width_percent": 0.432}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03703, "duration_str": "37.03ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 8.559, "width_percent": 0.198}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05148, "duration_str": "51.48ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 8.757, "width_percent": 0.276}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06203, "duration_str": "62.03ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 9.033, "width_percent": 0.332}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04243, "duration_str": "42.43ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 9.365, "width_percent": 0.227}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06964000000000001, "duration_str": "69.64ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 9.592, "width_percent": 0.373}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09135, "duration_str": "91.35ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 9.965, "width_percent": 0.489}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10955, "duration_str": "110ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 10.454, "width_percent": 0.586}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10822, "duration_str": "108ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 11.04, "width_percent": 0.579}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.18119, "duration_str": "181ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 11.619, "width_percent": 0.97}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.23976, "duration_str": "240ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 12.589, "width_percent": 1.283}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08212, "duration_str": "82.12ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 13.873, "width_percent": 0.44}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.22338, "duration_str": "223ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 14.312, "width_percent": 1.196}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 15.508, "width_percent": 0.005}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 15.513, "width_percent": 0.005}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 15.518, "width_percent": 0.005}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 15.523, "width_percent": 0.005}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 15.528, "width_percent": 0.005}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05987, "duration_str": "59.87ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 15.532, "width_percent": 0.32}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.11252, "duration_str": "113ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 15.853, "width_percent": 0.602}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 16.455, "width_percent": 0.006}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 16.462, "width_percent": 0.006}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 16.468, "width_percent": 0.006}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 16.473, "width_percent": 0.004}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 16.478, "width_percent": 0.004}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 16.482, "width_percent": 0.004}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00202, "duration_str": "2.02ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 16.486, "width_percent": 0.011}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 16.496, "width_percent": 0.005}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.032350000000000004, "duration_str": "32.35ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 16.502, "width_percent": 0.173}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04636, "duration_str": "46.36ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 16.675, "width_percent": 0.248}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02885, "duration_str": "28.85ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 16.923, "width_percent": 0.154}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03046, "duration_str": "30.46ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 17.077, "width_percent": 0.163}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06172, "duration_str": "61.72ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 17.24, "width_percent": 0.33}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.17599, "duration_str": "176ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 17.571, "width_percent": 0.942}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.15228, "duration_str": "152ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 18.513, "width_percent": 0.815}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07676999999999999, "duration_str": "76.77ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 19.328, "width_percent": 0.411}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06665, "duration_str": "66.65ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 19.739, "width_percent": 0.357}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.13438, "duration_str": "134ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 20.096, "width_percent": 0.719}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.17443, "duration_str": "174ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 20.815, "width_percent": 0.934}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.12351000000000001, "duration_str": "124ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 21.749, "width_percent": 0.661}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.27393, "duration_str": "274ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 22.41, "width_percent": 1.466}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.38839999999999997, "duration_str": "388ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 23.876, "width_percent": 2.079}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10635, "duration_str": "106ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 25.955, "width_percent": 0.569}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.13, "duration_str": "130ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 26.524, "width_percent": 0.696}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.13172999999999999, "duration_str": "132ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 27.22, "width_percent": 0.705}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.21356999999999998, "duration_str": "214ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 27.926, "width_percent": 1.143}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.11655, "duration_str": "117ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 29.069, "width_percent": 0.624}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.27918, "duration_str": "279ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 29.693, "width_percent": 1.494}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01751, "duration_str": "17.51ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 31.187, "width_percent": 0.094}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0034500000000000004, "duration_str": "3.45ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 31.281, "width_percent": 0.018}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.14164, "duration_str": "142ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 31.299, "width_percent": 0.758}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08459, "duration_str": "84.59ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 32.057, "width_percent": 0.453}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04151, "duration_str": "41.51ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 32.51, "width_percent": 0.222}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05849, "duration_str": "58.49ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 32.732, "width_percent": 0.313}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0251, "duration_str": "25.1ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 33.046, "width_percent": 0.134}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07575, "duration_str": "75.75ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 33.18, "width_percent": 0.405}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.13294999999999998, "duration_str": "133ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 33.585, "width_percent": 0.712}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.25688, "duration_str": "257ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 34.297, "width_percent": 1.375}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09813, "duration_str": "98.13ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 35.672, "width_percent": 0.525}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.17829, "duration_str": "178ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 36.197, "width_percent": 0.954}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.24639, "duration_str": "246ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 37.152, "width_percent": 1.319}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.34763, "duration_str": "348ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 38.471, "width_percent": 1.861}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00159, "duration_str": "1.59ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 40.331, "width_percent": 0.009}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0014, "duration_str": "1.4ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 40.34, "width_percent": 0.007}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08383, "duration_str": "83.83ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 40.348, "width_percent": 0.449}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09765, "duration_str": "97.65ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 40.796, "width_percent": 0.523}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09472, "duration_str": "94.72ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 41.319, "width_percent": 0.507}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.046380000000000005, "duration_str": "46.38ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 41.826, "width_percent": 0.248}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01821, "duration_str": "18.21ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 42.074, "width_percent": 0.097}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05683, "duration_str": "56.83ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 42.172, "width_percent": 0.304}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05133, "duration_str": "51.33ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 42.476, "width_percent": 0.275}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08259999999999999, "duration_str": "82.6ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 42.751, "width_percent": 0.442}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07592, "duration_str": "75.92ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 43.193, "width_percent": 0.406}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07147, "duration_str": "71.47ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 43.599, "width_percent": 0.383}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.12713, "duration_str": "127ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 43.982, "width_percent": 0.681}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.25556, "duration_str": "256ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 44.662, "width_percent": 1.368}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.12462000000000001, "duration_str": "125ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 46.03, "width_percent": 0.667}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07362, "duration_str": "73.62ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 46.697, "width_percent": 0.394}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04511, "duration_str": "45.11ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 47.091, "width_percent": 0.241}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.016730000000000002, "duration_str": "16.73ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 47.333, "width_percent": 0.09}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09403, "duration_str": "94.03ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 47.423, "width_percent": 0.503}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.19038, "duration_str": "190ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 47.926, "width_percent": 1.019}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08106999999999999, "duration_str": "81.07ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 48.945, "width_percent": 0.434}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.19635, "duration_str": "196ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 49.379, "width_percent": 1.051}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.16834, "duration_str": "168ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 50.43, "width_percent": 0.901}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09848, "duration_str": "98.48ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 51.331, "width_percent": 0.527}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.18541, "duration_str": "185ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 51.858, "width_percent": 0.992}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08857999999999999, "duration_str": "88.58ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 52.851, "width_percent": 0.474}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05146, "duration_str": "51.46ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 53.325, "width_percent": 0.275}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05122, "duration_str": "51.22ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 53.6, "width_percent": 0.274}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.11766, "duration_str": "118ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 53.874, "width_percent": 0.63}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.17233, "duration_str": "172ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 54.504, "width_percent": 0.922}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.13472, "duration_str": "135ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 55.427, "width_percent": 0.721}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09362000000000001, "duration_str": "93.62ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 56.148, "width_percent": 0.501}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07148, "duration_str": "71.48ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 56.649, "width_percent": 0.383}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.027719999999999998, "duration_str": "27.72ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 57.032, "width_percent": 0.148}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0456, "duration_str": "45.6ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 57.18, "width_percent": 0.244}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04405, "duration_str": "44.05ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 57.424, "width_percent": 0.236}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10387, "duration_str": "104ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 57.66, "width_percent": 0.556}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07376, "duration_str": "73.76ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 58.216, "width_percent": 0.395}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.062340000000000007, "duration_str": "62.34ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 58.611, "width_percent": 0.334}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06713, "duration_str": "67.13ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 58.945, "width_percent": 0.359}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0546, "duration_str": "54.6ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 59.304, "width_percent": 0.292}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10317, "duration_str": "103ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 59.596, "width_percent": 0.552}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10145, "duration_str": "101ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 60.148, "width_percent": 0.543}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04555, "duration_str": "45.55ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 60.691, "width_percent": 0.244}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05644, "duration_str": "56.44ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 60.935, "width_percent": 0.302}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05094, "duration_str": "50.94ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 61.237, "width_percent": 0.273}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04082, "duration_str": "40.82ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 61.51, "width_percent": 0.219}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03302, "duration_str": "33.02ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 61.729, "width_percent": 0.177}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02638, "duration_str": "26.38ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 61.905, "width_percent": 0.141}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06269, "duration_str": "62.69ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 62.047, "width_percent": 0.336}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.11208, "duration_str": "112ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 62.382, "width_percent": 0.6}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07023, "duration_str": "70.23ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 62.982, "width_percent": 0.376}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07806999999999999, "duration_str": "78.07ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 63.358, "width_percent": 0.418}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.084, "duration_str": "84ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 63.776, "width_percent": 0.45}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.18425, "duration_str": "184ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 64.226, "width_percent": 0.986}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07368000000000001, "duration_str": "73.68ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 65.212, "width_percent": 0.394}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06001, "duration_str": "60.01ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 65.606, "width_percent": 0.321}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05803, "duration_str": "58.03ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 65.927, "width_percent": 0.311}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09297, "duration_str": "92.97ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 66.238, "width_percent": 0.498}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.15725999999999998, "duration_str": "157ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 66.736, "width_percent": 0.842}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.14526, "duration_str": "145ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 67.578, "width_percent": 0.778}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.21458000000000002, "duration_str": "215ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 68.355, "width_percent": 1.149}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10481, "duration_str": "105ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 69.504, "width_percent": 0.561}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10736, "duration_str": "107ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 70.065, "width_percent": 0.575}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.11234999999999999, "duration_str": "112ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 70.639, "width_percent": 0.601}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10617, "duration_str": "106ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 71.241, "width_percent": 0.568}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0485, "duration_str": "48.5ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 71.809, "width_percent": 0.26}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08449, "duration_str": "84.49ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 72.069, "width_percent": 0.452}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.16428, "duration_str": "164ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 72.521, "width_percent": 0.879}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0878, "duration_str": "87.8ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 73.4, "width_percent": 0.47}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07973999999999999, "duration_str": "79.74ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 73.87, "width_percent": 0.427}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07723999999999999, "duration_str": "77.24ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 74.297, "width_percent": 0.413}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07148, "duration_str": "71.48ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 74.711, "width_percent": 0.383}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08451, "duration_str": "84.51ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 75.093, "width_percent": 0.452}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05891, "duration_str": "58.91ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 75.546, "width_percent": 0.315}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0167, "duration_str": "16.7ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 75.861, "width_percent": 0.089}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02063, "duration_str": "20.63ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 75.95, "width_percent": 0.11}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07254000000000001, "duration_str": "72.54ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 76.061, "width_percent": 0.388}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09591, "duration_str": "95.91ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 76.449, "width_percent": 0.513}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07286, "duration_str": "72.86ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 76.963, "width_percent": 0.39}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05889, "duration_str": "58.89ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 77.353, "width_percent": 0.315}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10298, "duration_str": "103ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 77.668, "width_percent": 0.551}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07418999999999999, "duration_str": "74.19ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 78.219, "width_percent": 0.397}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05261, "duration_str": "52.61ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 78.616, "width_percent": 0.282}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.26438, "duration_str": "264ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 78.898, "width_percent": 1.415}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.15769, "duration_str": "158ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 80.313, "width_percent": 0.844}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.13926, "duration_str": "139ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 81.157, "width_percent": 0.745}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.15691, "duration_str": "157ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 81.903, "width_percent": 0.84}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.19903, "duration_str": "199ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 82.743, "width_percent": 1.065}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.1696, "duration_str": "170ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 83.808, "width_percent": 0.908}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.13562000000000002, "duration_str": "136ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 84.716, "width_percent": 0.726}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.1175, "duration_str": "118ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 85.442, "width_percent": 0.629}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.037590000000000005, "duration_str": "37.59ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 86.071, "width_percent": 0.201}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04909, "duration_str": "49.09ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 86.272, "width_percent": 0.263}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05526, "duration_str": "55.26ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 86.535, "width_percent": 0.296}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01149, "duration_str": "11.49ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 86.831, "width_percent": 0.062}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00394, "duration_str": "3.94ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 86.892, "width_percent": 0.021}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00544, "duration_str": "5.44ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 86.913, "width_percent": 0.029}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 86.942, "width_percent": 0.005}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00145, "duration_str": "1.45ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 86.947, "width_percent": 0.008}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 86.955, "width_percent": 0.007}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04777, "duration_str": "47.77ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 86.962, "width_percent": 0.256}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08031, "duration_str": "80.31ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 87.217, "width_percent": 0.43}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08874, "duration_str": "88.74ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 87.647, "width_percent": 0.475}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02228, "duration_str": "22.28ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 88.122, "width_percent": 0.119}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 88.241, "width_percent": 0.004}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.245, "width_percent": 0.006}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.251, "width_percent": 0.006}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00198, "duration_str": "1.98ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.257, "width_percent": 0.011}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.268, "width_percent": 0.005}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00219, "duration_str": "2.19ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.273, "width_percent": 0.012}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0025800000000000003, "duration_str": "2.58ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 88.285, "width_percent": 0.014}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 88.298, "width_percent": 0.006}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0016699999999999998, "duration_str": "1.67ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.304, "width_percent": 0.009}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00115, "duration_str": "1.15ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.313, "width_percent": 0.006}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00393, "duration_str": "3.93ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.319, "width_percent": 0.021}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00115, "duration_str": "1.15ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.34, "width_percent": 0.006}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00287, "duration_str": "2.87ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.347, "width_percent": 0.015}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0033799999999999998, "duration_str": "3.38ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 88.362, "width_percent": 0.018}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0018, "duration_str": "1.8ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 88.38, "width_percent": 0.01}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00318, "duration_str": "3.18ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.39, "width_percent": 0.017}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00163, "duration_str": "1.63ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.407, "width_percent": 0.009}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00267, "duration_str": "2.67ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.415, "width_percent": 0.014}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.019620000000000002, "duration_str": "19.62ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.43, "width_percent": 0.105}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00375, "duration_str": "3.75ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.535, "width_percent": 0.02}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00467, "duration_str": "4.67ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 88.555, "width_percent": 0.025}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0038900000000000002, "duration_str": "3.89ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 88.58, "width_percent": 0.021}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00883, "duration_str": "8.83ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.601, "width_percent": 0.047}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.648, "width_percent": 0.005}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.653, "width_percent": 0.005}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.658, "width_percent": 0.004}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.662, "width_percent": 0.004}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0023799999999999997, "duration_str": "2.38ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 88.665, "width_percent": 0.013}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00436, "duration_str": "4.36ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 88.678, "width_percent": 0.023}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00796, "duration_str": "7.96ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.701, "width_percent": 0.043}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00948, "duration_str": "9.48ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.744, "width_percent": 0.051}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.795, "width_percent": 0.006}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00156, "duration_str": "1.56ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.801, "width_percent": 0.008}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00157, "duration_str": "1.57ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.809, "width_percent": 0.008}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0033599999999999997, "duration_str": "3.36ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 88.817, "width_percent": 0.018}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00882, "duration_str": "8.82ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 88.835, "width_percent": 0.047}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0016799999999999999, "duration_str": "1.68ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.883, "width_percent": 0.009}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00225, "duration_str": "2.25ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.892, "width_percent": 0.012}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.045399999999999996, "duration_str": "45.4ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 88.904, "width_percent": 0.243}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.023440000000000003, "duration_str": "23.44ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 89.147, "width_percent": 0.125}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02604, "duration_str": "26.04ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 89.272, "width_percent": 0.139}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0255, "duration_str": "25.5ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 89.411, "width_percent": 0.136}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08003, "duration_str": "80.03ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 89.548, "width_percent": 0.428}, {"sql": "select * from `notes` where `notes`.`id` = 10219 and `notes`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10219"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.14672, "duration_str": "147ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 89.976, "width_percent": 0.785}, {"sql": "select * from `matieres` where `matieres`.`id` in (202) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.12004000000000001, "duration_str": "120ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 90.762, "width_percent": 0.643}, {"sql": "select * from `ues` where `ues`.`id` in (163) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.12893000000000002, "duration_str": "129ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 91.404, "width_percent": 0.69}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 32, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.18782, "duration_str": "188ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 92.094, "width_percent": 1.005}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1002}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.13225, "duration_str": "132ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1002", "connection": "imsaaapp", "start_percent": 93.1, "width_percent": 0.708}, {"sql": "select `id`, `nom` from `matieres` where exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1009}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10041, "duration_str": "100ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1009", "connection": "imsaaapp", "start_percent": 93.808, "width_percent": 0.537}, {"sql": "select `id`, `nom` from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 1012}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 855}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09672, "duration_str": "96.72ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:1012", "connection": "imsaaapp", "start_percent": 94.345, "width_percent": 0.518}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.07890000000000001, "duration_str": "78.9ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 94.863, "width_percent": 0.422}, {"sql": "select `id`, `user_id`, `parcour_id`, `niveau_id`, `annee_universitaire_id`, `created_at`, `deleted_at` from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null order by `created_at` desc limit 10 offset 690", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.09919, "duration_str": "99.19ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 95.285, "width_percent": 0.531}, {"sql": "select `id`, `nom`, `prenom`, `telephone1`, `sexe`, `photo` from `users` where `users`.`id` in (84, 85, 86, 87, 88, 89, 109, 110, 129, 130) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.09309, "duration_str": "93.09ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 95.816, "width_percent": 0.498}, {"sql": "select `id`, `sigle`, `nom` from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.07997, "duration_str": "79.97ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 96.315, "width_percent": 0.428}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.16166, "duration_str": "162ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 96.743, "width_percent": 0.865}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.189, "duration_str": "189ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 97.608, "width_percent": 1.012}, {"sql": "select `id`, `nom` from `semestres` where `niveau_id` = 1 and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 204}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.25786000000000003, "duration_str": "258ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:204", "connection": "imsaaapp", "start_percent": 98.62, "width_percent": 1.38}]}, "models": {"data": {"App\\Models\\InscriptionStudent": 10, "App\\Models\\Ue": 32, "App\\Models\\Parcour": 34, "App\\Models\\AnneeUniversitaire": 2, "App\\Models\\Niveau": 2, "App\\Models\\Semestre": 3, "App\\Models\\TypeNote": 134, "App\\Models\\Matiere": 626, "App\\Models\\Note": 87, "App\\Models\\User": 11}, "count": 941}, "livewire": {"data": {"etudiant #P9t3Oi1et74vp9qCsbMQ": "array:5 [\n  \"data\" => array:56 [\n    \"showCreateModal\" => false\n    \"showEditModal\" => false\n    \"showDeleteModal\" => false\n    \"showRestoreModal\" => false\n    \"viewSupprimesMode\" => false\n    \"selectedEtudiantId\" => null\n    \"selectedUserId\" => null\n    \"etuName\" => null\n    \"newUser\" => []\n    \"editUser\" => []\n    \"editParcours\" => []\n    \"query\" => null\n    \"filtreParcours\" => null\n    \"filtreNiveau\" => null\n    \"filtreAnnee\" => null\n    \"perPage\" => 10\n    \"isLoading\" => false\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"showFilters\" => true\n    \"compactView\" => false\n    \"bulkAction\" => \"\"\n    \"totalEtudiants\" => 720\n    \"totalSupprimés\" => 102\n    \"selectedItems\" => []\n    \"showNotes\" => true\n    \"selectedStudentName\" => \"BEHAVANA Gauffit\"\n    \"noteUserId\" => 109\n    \"noteParcourId\" => null\n    \"noteMatiereId\" => null\n    \"noteTypeId\" => null\n    \"noteValeur\" => null\n    \"noteObservation\" => null\n    \"noteActionMode\" => \"list\"\n    \"selectedNoteId\" => null\n    \"notes\" => Illuminate\\Database\\Eloquent\\Collection {#4814\n      #items: array:55 [\n        0 => App\\Models\\Note {#1783\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10219\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 3\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 679\n            \"created_at\" => \"2023-09-07 16:39:25\"\n            \"updated_at\" => \"2023-09-07 16:39:25\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10219\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 3\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 679\n            \"created_at\" => \"2023-09-07 16:39:25\"\n            \"updated_at\" => \"2023-09-07 16:39:25\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1821 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1835 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Note {#1782\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10179\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 678\n            \"created_at\" => \"2023-09-07 16:39:23\"\n            \"updated_at\" => \"2023-09-07 16:39:23\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10179\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 678\n            \"created_at\" => \"2023-09-07 16:39:23\"\n            \"updated_at\" => \"2023-09-07 16:39:23\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1821 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1837 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Note {#1781\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10139\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 677\n            \"created_at\" => \"2023-09-07 16:39:02\"\n            \"updated_at\" => \"2023-09-07 16:39:02\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10139\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 677\n            \"created_at\" => \"2023-09-07 16:39:02\"\n            \"updated_at\" => \"2023-09-07 16:39:02\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1819 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1669 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Note {#1780\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10099\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 676\n            \"created_at\" => \"2023-09-07 16:39:01\"\n            \"updated_at\" => \"2023-09-07 16:39:01\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10099\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 676\n            \"created_at\" => \"2023-09-07 16:39:01\"\n            \"updated_at\" => \"2023-09-07 16:39:01\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1819 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1837 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Note {#1779\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10059\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 675\n            \"created_at\" => \"2023-09-07 16:38:57\"\n            \"updated_at\" => \"2023-09-07 16:38:57\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10059\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 675\n            \"created_at\" => \"2023-09-07 16:38:57\"\n            \"updated_at\" => \"2023-09-07 16:38:57\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1819 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1837 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Note {#1778\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10019\n            \"valeur\" => 9.5\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 196\n            \"history_note_id\" => 674\n            \"created_at\" => \"2023-09-07 16:38:52\"\n            \"updated_at\" => \"2023-09-07 16:38:52\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10019\n            \"valeur\" => 9.5\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 196\n            \"history_note_id\" => 674\n            \"created_at\" => \"2023-09-07 16:38:52\"\n            \"updated_at\" => \"2023-09-07 16:38:52\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1815 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1837 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Note {#1777\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9979\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 673\n            \"created_at\" => \"2023-09-07 16:38:49\"\n            \"updated_at\" => \"2023-09-07 16:38:49\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9979\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 673\n            \"created_at\" => \"2023-09-07 16:38:49\"\n            \"updated_at\" => \"2023-09-07 16:38:49\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1816 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1669 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Note {#1776\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9939\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 672\n            \"created_at\" => \"2023-09-07 16:38:48\"\n            \"updated_at\" => \"2023-09-07 16:38:48\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9939\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 672\n            \"created_at\" => \"2023-09-07 16:38:48\"\n            \"updated_at\" => \"2023-09-07 16:38:48\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1816 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1837 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Note {#1775\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9899\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 671\n            \"created_at\" => \"2023-09-07 16:38:46\"\n            \"updated_at\" => \"2023-09-07 16:38:46\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9899\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 671\n            \"created_at\" => \"2023-09-07 16:38:46\"\n            \"updated_at\" => \"2023-09-07 16:38:46\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1817 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1837 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        9 => App\\Models\\Note {#1774\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9859\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 670\n            \"created_at\" => \"2023-09-07 16:38:45\"\n            \"updated_at\" => \"2023-09-07 16:38:45\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9859\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 670\n            \"created_at\" => \"2023-09-07 16:38:45\"\n            \"updated_at\" => \"2023-09-07 16:38:45\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1817 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1669 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        10 => App\\Models\\Note {#1773\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9819\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n            \"history_note_id\" => 669\n            \"created_at\" => \"2023-09-07 16:38:42\"\n            \"updated_at\" => \"2023-09-07 16:38:42\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9819\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n            \"history_note_id\" => 669\n            \"created_at\" => \"2023-09-07 16:38:42\"\n            \"updated_at\" => \"2023-09-07 16:38:42\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1818 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1669 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        11 => App\\Models\\Note {#1772\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9779\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n             …4\n          ]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        12 => App\\Models\\Note {#1771\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        13 => App\\Models\\Note {#1770\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        14 => App\\Models\\Note {#1769\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        15 => App\\Models\\Note {#1768\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        16 => App\\Models\\Note {#1767\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        17 => App\\Models\\Note {#1766\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        18 => App\\Models\\Note {#1765\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        19 => App\\Models\\Note {#1764\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        20 => App\\Models\\Note {#1763\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        21 => App\\Models\\Note {#1762\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        22 => App\\Models\\Note {#1761\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        23 => App\\Models\\Note {#1760\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        24 => App\\Models\\Note {#1759\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        25 => App\\Models\\Note {#1758\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        26 => App\\Models\\Note {#1757\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        27 => App\\Models\\Note {#1756\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        28 => App\\Models\\Note {#597\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        29 => App\\Models\\Note {#668\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        30 => App\\Models\\Note {#1727\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        31 => App\\Models\\Note {#1728\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        32 => App\\Models\\Note {#1729\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        33 => App\\Models\\Note {#1730\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        34 => App\\Models\\Note {#1731\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        35 => App\\Models\\Note {#1732\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        36 => App\\Models\\Note {#1733\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        37 => App\\Models\\Note {#1734\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        38 => App\\Models\\Note {#1735\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        39 => App\\Models\\Note {#1736\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        40 => App\\Models\\Note {#1737\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        41 => App\\Models\\Note {#1738\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        42 => App\\Models\\Note {#1739\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        43 => App\\Models\\Note {#1740\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        44 => App\\Models\\Note {#1741\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        45 => App\\Models\\Note {#1742\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        46 => App\\Models\\Note {#1743\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        47 => App\\Models\\Note {#1744\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        48 => App\\Models\\Note {#1745\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        49 => App\\Models\\Note {#1746\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        50 => App\\Models\\Note {#1747\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        51 => App\\Models\\Note {#1748\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        52 => App\\Models\\Note {#1749\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        53 => App\\Models\\Note {#1750\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        54 => App\\Models\\Note {#1752\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"matieres\" => []\n    \"noteTypes\" => Illuminate\\Database\\Eloquent\\Collection {#4811\n      #items: array:3 [\n        0 => App\\Models\\TypeNote {#1848\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [ …1]\n        }\n        1 => App\\Models\\TypeNote {#1696\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [ …1]\n        }\n        2 => App\\Models\\TypeNote {#1884\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [ …1]\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_semestre\" => App\\Models\\Semestre {#1888\n      #connection: \"mysql\"\n      #table: \"semestres\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"Semestre 1\"\n        \"niveau_id\" => 1\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"Semestre 1\"\n        \"niveau_id\" => 1\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"nom\"\n        1 => \"niveau_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1897\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1906\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_parcour\" => App\\Models\\Parcour {#1915\n      #connection: \"mysql\"\n      #table: \"parcours\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:3 [\n        0 => \"nom\"\n        1 => \"sigle\"\n        2 => \"mention_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"selectedSemestreId\" => \"\"\n    \"viewMode\" => \"table\"\n    \"searchQuery\" => \"\"\n    \"sortBy\" => \"created_at_desc\"\n    \"selectedNoteType\" => \"\"\n    \"autoSave\" => true\n    \"saveStatus\" => []\n    \"notesStatistics\" => array:5 [\n      \"average\" => 11.638\n      \"best\" => 18\n      \"worst\" => 0\n      \"count\" => 55\n      \"success_rate\" => 69.090909090909\n    ]\n    \"validationErrors\" => []\n    \"isValidating\" => false\n    \"showEditNoteModal\" => true\n    \"editNote\" => array:8 [\n      \"id\" => 10219\n      \"matiere_id\" => 202\n      \"type_note_id\" => 3\n      \"valeur\" => 15.0\n      \"observation\" => \"\"\n      \"student_name\" => \"BEHAVANA Gauffit\"\n      \"available_matieres\" => array:18 [\n        0 => array:2 [\n          \"id\" => 33\n          \"nom\" => \"ECONOMIE DE TRANSPORT \"\n        ]\n        1 => array:2 [\n          \"id\" => 34\n          \"nom\" => \"ITALIEN\"\n        ]\n        2 => array:2 [\n          \"id\" => 35\n          \"nom\" => \"LOGISTIQUE \"\n        ]\n        3 => array:2 [\n          \"id\" => 36\n          \"nom\" => \"ORGANISATION D'ENTREPRISE \"\n        ]\n        4 => array:2 [\n          \"id\" => 37\n          \"nom\" => \"TECHNIQUE DU COMMERCE INTERNATIONAL \"\n        ]\n        5 => array:2 [\n          \"id\" => 38\n          \"nom\" => \"GÉOGRAPHIE DES ECHANGES \"\n        ]\n        6 => array:2 [\n          \"id\" => 41\n          \"nom\" => \"INFORMATIQUE BUREAUTIQUE \"\n        ]\n        7 => array:2 [\n          \"id\" => 42\n          \"nom\" => \"VISITE D'ENTREPRISE \"\n        ]\n        8 => array:2 [\n          \"id\" => 43\n          \"nom\" => \"DROIT COMMERCIAL \"\n        ]\n        9 => array:2 [\n          \"id\" => 45\n          \"nom\" => \"MATHÉMATIQUE FINANCIÈRE\"\n        ]\n        10 => array:2 [\n          \"id\" => 196\n          \"nom\" => \"COMPTABILITE\"\n        ]\n        11 => array:2 [\n          \"id\" => 197\n          \"nom\" => \"GESTION DE PROJET \"\n        ]\n        12 => array:2 [\n          \"id\" => 198\n          \"nom\" => \"TRANSIT ET DOUANE\"\n        ]\n        13 => array:2 [\n          \"id\" => 199\n          \"nom\" => \"DROIT  DE TRANSPORT \"\n        ]\n        14 => array:2 [\n          \"id\" => 200\n          \"nom\" => \"ITALIEN\"\n        ]\n        15 => array:2 [\n          \"id\" => 201\n          \"nom\" => \"MARKETING\"\n        ]\n        16 => array:2 [\n          \"id\" => 202\n          \"nom\" => \"PROJET TUTORET \"\n        ]\n        17 => array:2 [\n          \"id\" => 203\n          \"nom\" => \"VISITE D'ENTREPRISE \"\n        ]\n      ]\n      \"available_types\" => array:3 [\n        0 => array:2 [\n          \"id\" => 1\n          \"nom\" => \"Partiel 1\"\n        ]\n        1 => array:2 [\n          \"id\" => 2\n          \"nom\" => \"Partiel 2\"\n        ]\n        2 => array:2 [\n          \"id\" => 3\n          \"nom\" => \"Examen\"\n        ]\n      ]\n    ]\n    \"page\" => \"70\"\n    \"paginators\" => array:1 [\n      \"page\" => \"70\"\n    ]\n  ]\n  \"name\" => \"etudiant\"\n  \"view\" => \"livewire.deraq.etudiant.notes\"\n  \"component\" => \"App\\Http\\Livewire\\Etudiant\"\n  \"id\" => \"P9t3Oi1et74vp9qCsbMQ\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestions/etudiants?page=70\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751977751\n]"}, "request": {"path_info": "/livewire/message/etudiant", "status_code": "<pre class=sf-dump id=sf-dump-732314512 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-732314512\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-219145697 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-219145697\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1669382706 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">P9t3Oi1et74vp9qCsbMQ</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"18 characters\">gestions/etudiants</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">5353f3d8</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:56</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>showCreateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEditModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showDeleteModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showRestoreModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>viewSupprimesMode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>selectedEtudiantId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedUserId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>etuName</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>newUser</span>\" => []\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>editParcours</span>\" => []\n      \"<span class=sf-dump-key>query</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreParcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>isLoading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>showFilters</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>compactView</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>bulkAction</span>\" => \"\"\n      \"<span class=sf-dump-key>totalEtudiants</span>\" => <span class=sf-dump-num>720</span>\n      \"<span class=sf-dump-key>totalSupprim&#233;s</span>\" => <span class=sf-dump-num>102</span>\n      \"<span class=sf-dump-key>selectedItems</span>\" => []\n      \"<span class=sf-dump-key>showNotes</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>selectedStudentName</span>\" => \"<span class=sf-dump-str title=\"16 characters\">BEHAVANA Gauffit</span>\"\n      \"<span class=sf-dump-key>noteUserId</span>\" => <span class=sf-dump-num>109</span>\n      \"<span class=sf-dump-key>noteParcourId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteMatiereId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteTypeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteValeur</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteObservation</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteActionMode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">list</span>\"\n      \"<span class=sf-dump-key>selectedNoteId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>notes</span>\" => []\n      \"<span class=sf-dump-key>matieres</span>\" => []\n      \"<span class=sf-dump-key>noteTypes</span>\" => []\n      \"<span class=sf-dump-key>current_semestre</span>\" => []\n      \"<span class=sf-dump-key>current_niveau</span>\" => []\n      \"<span class=sf-dump-key>current_annee</span>\" => []\n      \"<span class=sf-dump-key>current_parcour</span>\" => []\n      \"<span class=sf-dump-key>selectedSemestreId</span>\" => \"\"\n      \"<span class=sf-dump-key>viewMode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">table</span>\"\n      \"<span class=sf-dump-key>searchQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>sortBy</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at_desc</span>\"\n      \"<span class=sf-dump-key>selectedNoteType</span>\" => \"\"\n      \"<span class=sf-dump-key>autoSave</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>saveStatus</span>\" => []\n      \"<span class=sf-dump-key>notesStatistics</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>average</span>\" => <span class=sf-dump-num>11.638</span>\n        \"<span class=sf-dump-key>best</span>\" => <span class=sf-dump-num>18</span>\n        \"<span class=sf-dump-key>worst</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>55</span>\n        \"<span class=sf-dump-key>success_rate</span>\" => <span class=sf-dump-num>69.090909090909</span>\n      </samp>]\n      \"<span class=sf-dump-key>validationErrors</span>\" => []\n      \"<span class=sf-dump-key>isValidating</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEditNoteModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>editNote</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10219</span>\n        \"<span class=sf-dump-key>matiere_id</span>\" => <span class=sf-dump-num>202</span>\n        \"<span class=sf-dump-key>type_note_id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>valeur</span>\" => <span class=sf-dump-num>15</span>\n        \"<span class=sf-dump-key>observation</span>\" => \"\"\n        \"<span class=sf-dump-key>student_name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">BEHAVANA Gauffit</span>\"\n        \"<span class=sf-dump-key>available_matieres</span>\" => <span class=sf-dump-note>array:18</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>33</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"22 characters\">ECONOMIE DE TRANSPORT </span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>34</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ITALIEN</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>35</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"11 characters\">LOGISTIQUE </span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>36</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"26 characters\">ORGANISATION D&#039;ENTREPRISE </span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>37</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"36 characters\">TECHNIQUE DU COMMERCE INTERNATIONAL </span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>38</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"24 characters\">G&#201;OGRAPHIE DES ECHANGES </span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>41</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"25 characters\">INFORMATIQUE BUREAUTIQUE </span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>42</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"20 characters\">VISITE D&#039;ENTREPRISE </span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>43</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"17 characters\">DROIT COMMERCIAL </span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>45</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"23 characters\">MATH&#201;MATIQUE FINANCI&#200;RE</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>196</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"12 characters\">COMPTABILITE</span>\"\n          </samp>]\n          <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>197</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"18 characters\">GESTION DE PROJET </span>\"\n          </samp>]\n          <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>198</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"17 characters\">TRANSIT ET DOUANE</span>\"\n          </samp>]\n          <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>199</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"20 characters\">DROIT  DE TRANSPORT </span>\"\n          </samp>]\n          <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>200</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ITALIEN</span>\"\n          </samp>]\n          <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>201</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">MARKETING</span>\"\n          </samp>]\n          <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>202</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"15 characters\">PROJET TUTORET </span>\"\n          </samp>]\n          <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>203</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"20 characters\">VISITE D&#039;ENTREPRISE </span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>available_types</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Partiel 1</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Partiel 2</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Examen</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Note</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:55</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>10179</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>10139</span>\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-num>10099</span>\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-num>10059</span>\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-num>10019</span>\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-num>9979</span>\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-num>9939</span>\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-num>9899</span>\n            <span class=sf-dump-index>9</span> => <span class=sf-dump-num>9859</span>\n            <span class=sf-dump-index>10</span> => <span class=sf-dump-num>9819</span>\n            <span class=sf-dump-index>11</span> => <span class=sf-dump-num>9779</span>\n            <span class=sf-dump-index>12</span> => <span class=sf-dump-num>9739</span>\n            <span class=sf-dump-index>13</span> => <span class=sf-dump-num>9699</span>\n            <span class=sf-dump-index>14</span> => <span class=sf-dump-num>9659</span>\n            <span class=sf-dump-index>15</span> => <span class=sf-dump-num>9619</span>\n            <span class=sf-dump-index>16</span> => <span class=sf-dump-num>8520</span>\n            <span class=sf-dump-index>17</span> => <span class=sf-dump-num>8480</span>\n            <span class=sf-dump-index>18</span> => <span class=sf-dump-num>8415</span>\n            <span class=sf-dump-index>19</span> => <span class=sf-dump-num>8356</span>\n            <span class=sf-dump-index>20</span> => <span class=sf-dump-num>8302</span>\n            <span class=sf-dump-index>21</span> => <span class=sf-dump-num>8262</span>\n            <span class=sf-dump-index>22</span> => <span class=sf-dump-num>8012</span>\n            <span class=sf-dump-index>23</span> => <span class=sf-dump-num>7874</span>\n            <span class=sf-dump-index>24</span> => <span class=sf-dump-num>7834</span>\n            <span class=sf-dump-index>25</span> => <span class=sf-dump-num>4575</span>\n            <span class=sf-dump-index>26</span> => <span class=sf-dump-num>4518</span>\n            <span class=sf-dump-index>27</span> => <span class=sf-dump-num>4461</span>\n            <span class=sf-dump-index>28</span> => <span class=sf-dump-num>4368</span>\n            <span class=sf-dump-index>29</span> => <span class=sf-dump-num>4311</span>\n            <span class=sf-dump-index>30</span> => <span class=sf-dump-num>4180</span>\n            <span class=sf-dump-index>31</span> => <span class=sf-dump-num>4123</span>\n            <span class=sf-dump-index>32</span> => <span class=sf-dump-num>3453</span>\n            <span class=sf-dump-index>33</span> => <span class=sf-dump-num>3380</span>\n            <span class=sf-dump-index>34</span> => <span class=sf-dump-num>3323</span>\n            <span class=sf-dump-index>35</span> => <span class=sf-dump-num>3266</span>\n            <span class=sf-dump-index>36</span> => <span class=sf-dump-num>2734</span>\n            <span class=sf-dump-index>37</span> => <span class=sf-dump-num>2677</span>\n            <span class=sf-dump-index>38</span> => <span class=sf-dump-num>2324</span>\n            <span class=sf-dump-index>39</span> => <span class=sf-dump-num>1625</span>\n            <span class=sf-dump-index>40</span> => <span class=sf-dump-num>1568</span>\n            <span class=sf-dump-index>41</span> => <span class=sf-dump-num>1511</span>\n            <span class=sf-dump-index>42</span> => <span class=sf-dump-num>1454</span>\n            <span class=sf-dump-index>43</span> => <span class=sf-dump-num>1397</span>\n            <span class=sf-dump-index>44</span> => <span class=sf-dump-num>1295</span>\n            <span class=sf-dump-index>45</span> => <span class=sf-dump-num>1226</span>\n            <span class=sf-dump-index>46</span> => <span class=sf-dump-num>1163</span>\n            <span class=sf-dump-index>47</span> => <span class=sf-dump-num>1106</span>\n            <span class=sf-dump-index>48</span> => <span class=sf-dump-num>1049</span>\n            <span class=sf-dump-index>49</span> => <span class=sf-dump-num>992</span>\n            <span class=sf-dump-index>50</span> => <span class=sf-dump-num>885</span>\n            <span class=sf-dump-index>51</span> => <span class=sf-dump-num>828</span>\n            <span class=sf-dump-index>52</span> => <span class=sf-dump-num>771</span>\n            <span class=sf-dump-index>53</span> => <span class=sf-dump-num>714</span>\n            <span class=sf-dump-index>54</span> => <span class=sf-dump-num>657</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">matiere</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">typeNote</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>noteTypes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\TypeNote</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>2</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>3</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>models</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>current_semestre</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Semestre</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Niveau</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\AnneeUniversitaire</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_parcour</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Parcour</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">481ceed91ded9a99a6c7e77f11a072e44fe7740a83761fb75a7b55679933633c</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:32</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">fla2</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">ty7k</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">tepr</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">nr9j</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">nh73</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">97zh</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4r54</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">29m3</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qaj9</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pgn2</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1grz</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9ugn</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">ha3q</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">azb6</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">m9gc</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qr67</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">w7je</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cpkg</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3rxt</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">qggp</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">e3nz</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">a2hd</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">l9rw</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3isv</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0psg</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">tg0n</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">ouak</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">nnxp</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">a53y</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">z27s</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">qg7lg</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">16vy</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">showEditNoteForm</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10219</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669382706\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-437908789 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6359</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijd5cVVjeXlaMldhMTJrUk1iODEwVFE9PSIsInZhbHVlIjoiRHRCckt2aXhlOHVpemNpdHBlN2RTSXN3QTJCQWtVNDRQOGVNSGdBLzRQeU1sTk9RekFoY1dJUy81dEEvZ0tPUGRxQmVHb3hOL2tDNHM2aHlUeWoydzRFUTJjQi9ZZHFPN3grNVU5OUNickthMC9Nd0Z5MTAwaitGY0hLWUw2MVciLCJtYWMiOiIyZTg4MzQxMDJkN2NiYjNjODYzM2NlMmJiMjdhMWIxMTcxMDVmNjlhNTU0ODJlMGVjMjYyZGYzNDE5NjBiNTFmIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Im9sbVlNSm8rQlA0dVRadVRUT1pmY3c9PSIsInZhbHVlIjoiT0NnZkZiMHB1bWZKODVuT3pseXZnVkxIWklZUlVxYU1Sbzl6K3M3MExsejJ2ZzN0M28raVR6bjhzMEUzTjkxVENjS1VjRjdTVTd5ZjdiYlhPbUwxWWI2YytRNWdIUEJ3SVdnRCtuN0FkVjRUNGU2N3lqNWxnT2F1MjFkOUhXQjQiLCJtYWMiOiJiNjNkMmE2ZWM0ZTQ0NzBkMTZlMTdjMTU3M2Q2Y2ZhNDY4NDVjMDk3MWVhMDY5NDZhZDMxZTBjMzI1NTBjNjg1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437908789\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-462868540 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55296</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6359</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6359</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijd5cVVjeXlaMldhMTJrUk1iODEwVFE9PSIsInZhbHVlIjoiRHRCckt2aXhlOHVpemNpdHBlN2RTSXN3QTJCQWtVNDRQOGVNSGdBLzRQeU1sTk9RekFoY1dJUy81dEEvZ0tPUGRxQmVHb3hOL2tDNHM2aHlUeWoydzRFUTJjQi9ZZHFPN3grNVU5OUNickthMC9Nd0Z5MTAwaitGY0hLWUw2MVciLCJtYWMiOiIyZTg4MzQxMDJkN2NiYjNjODYzM2NlMmJiMjdhMWIxMTcxMDVmNjlhNTU0ODJlMGVjMjYyZGYzNDE5NjBiNTFmIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Im9sbVlNSm8rQlA0dVRadVRUT1pmY3c9PSIsInZhbHVlIjoiT0NnZkZiMHB1bWZKODVuT3pseXZnVkxIWklZUlVxYU1Sbzl6K3M3MExsejJ2ZzN0M28raVR6bjhzMEUzTjkxVENjS1VjRjdTVTd5ZjdiYlhPbUwxWWI2YytRNWdIUEJ3SVdnRCtuN0FkVjRUNGU2N3lqNWxnT2F1MjFkOUhXQjQiLCJtYWMiOiJiNjNkMmE2ZWM0ZTQ0NzBkMTZlMTdjMTU3M2Q2Y2ZhNDY4NDVjMDk3MWVhMDY5NDZhZDMxZTBjMzI1NTBjNjg1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751982230.7557</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751982230</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462868540\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2139674299 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IC6ylzyOLsmp8eqEPKRtYbNYwxNcQ3yBFxnhfM9e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2139674299\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-209355380 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 13:44:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlU1SE4xR2RlNWxMMktsOGNwbmltU2c9PSIsInZhbHVlIjoiNmFwVUU0UGthM1EwcXZSMTJxZ3hxZE1XOTRWRUI2dXNvNUgwN09LNnl4dG1LL0lLVVZ1R1UvdDFVOUZEQm9pbERaV0xsM0hnR3pJR1BWdndhSWt3aXF1Sllmd3NYY21pNHRiVDZ6ZHJ1UVZhb3pXTDhsQUcraXFnVGZXRnVuVFgiLCJtYWMiOiJiOTU2YTU1ZTMxZTE1ODhmYTE2NTIyNTk4ZDNhMDZlMTliM2QyODI0ZWE4YmIxM2M2MzhmYWEwNjM4ZDgyMGVmIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 15:44:13 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IkxqcDlhS3JTd3gvOTFUeTd1Nk94Zmc9PSIsInZhbHVlIjoiUytJektLelpHZ0xjeDdlSU51V3kxR3lsOUhFV3pnMVVOblk2b3Z3cmdNWlhlckMwTWlLY0lEM3FFME1NU3BINHZ2a29jeFdSWlE4QmZsNFhzdmk1dFFxdk9rbmNXVGdONXduSHFRS1VqbkJubncvNy9TOGxRNVloS05neGdOcnIiLCJtYWMiOiIwMjM2MWI2OTRiMjliZWJmMmZhODU0NmY5NWQ0NzRlMzU2NmFmNjQzOWI3MjQ1OTM2ZjJiYzA3MTIyNjMyNzViIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 15:44:13 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlU1SE4xR2RlNWxMMktsOGNwbmltU2c9PSIsInZhbHVlIjoiNmFwVUU0UGthM1EwcXZSMTJxZ3hxZE1XOTRWRUI2dXNvNUgwN09LNnl4dG1LL0lLVVZ1R1UvdDFVOUZEQm9pbERaV0xsM0hnR3pJR1BWdndhSWt3aXF1Sllmd3NYY21pNHRiVDZ6ZHJ1UVZhb3pXTDhsQUcraXFnVGZXRnVuVFgiLCJtYWMiOiJiOTU2YTU1ZTMxZTE1ODhmYTE2NTIyNTk4ZDNhMDZlMTliM2QyODI0ZWE4YmIxM2M2MzhmYWEwNjM4ZDgyMGVmIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 15:44:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IkxqcDlhS3JTd3gvOTFUeTd1Nk94Zmc9PSIsInZhbHVlIjoiUytJektLelpHZ0xjeDdlSU51V3kxR3lsOUhFV3pnMVVOblk2b3Z3cmdNWlhlckMwTWlLY0lEM3FFME1NU3BINHZ2a29jeFdSWlE4QmZsNFhzdmk1dFFxdk9rbmNXVGdONXduSHFRS1VqbkJubncvNy9TOGxRNVloS05neGdOcnIiLCJtYWMiOiIwMjM2MWI2OTRiMjliZWJmMmZhODU0NmY5NWQ0NzRlMzU2NmFmNjQzOWI3MjQ1OTM2ZjJiYzA3MTIyNjMyNzViIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 15:44:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209355380\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-203484797 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751977751</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203484797\", {\"maxDepth\":0})</script>\n"}}
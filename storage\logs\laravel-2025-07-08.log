[2025-07-08 16:44:13] local.INFO: Detected N+1 Query  
[2025-07-08 16:44:14] local.INFO: Model: App\Models\Note
Relation: App\Models\Matiere
Num-Called: 32
Call-Stack:
#20 \app\Http\Livewire\Etudiant.php:1002
#21 \app\Http\Livewire\Etudiant.php:855
#22 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#23 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#25 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#26 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#27 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#28 \vendor\livewire\livewire\src\LifecycleManager.php:89
#29 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#30 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#31 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#32 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#33 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#34 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#38 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#40 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#43 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#45 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#49 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
  
[2025-07-08 16:44:14] local.INFO: Model: App\Models\Matiere
Relation: App\Models\Ue
Num-Called: 32
Call-Stack:
#25 \app\Http\Livewire\Etudiant.php:1002
#26 \app\Http\Livewire\Etudiant.php:855
#27 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#28 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#29 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#30 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#31 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#32 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#33 \vendor\livewire\livewire\src\LifecycleManager.php:89
#34 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#35 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#36 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#37 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#38 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#39 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#41 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#43 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#45 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#47 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#48 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#49 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
  
[2025-07-08 16:44:14] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 32
Call-Stack:
#30 \app\Http\Livewire\Etudiant.php:1002
#31 \app\Http\Livewire\Etudiant.php:855
#32 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#33 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#34 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#35 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#36 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#37 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#38 \vendor\livewire\livewire\src\LifecycleManager.php:89
#39 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#40 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#41 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#42 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#43 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#44 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#46 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#49 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
  
[2025-07-08 16:44:14] local.INFO: Model: App\Models\Note
Relation: App\Models\TypeNote
Num-Called: 32
Call-Stack:
#20 \app\Http\Livewire\Etudiant.php:1002
#21 \app\Http\Livewire\Etudiant.php:855
#22 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#23 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#25 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#26 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#27 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#28 \vendor\livewire\livewire\src\LifecycleManager.php:89
#29 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#30 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#31 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#32 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#33 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#34 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#38 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#40 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#43 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#45 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#49 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
  
[2025-07-08 16:44:32] local.INFO: Detected N+1 Query  
[2025-07-08 16:44:32] local.INFO: Model: App\Models\Note
Relation: App\Models\Matiere
Num-Called: 16
Call-Stack:
#20 \app\Http\Livewire\Etudiant.php:1002
#21 \app\Http\Livewire\Etudiant.php:855
#22 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#23 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#25 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#26 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#27 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#28 \vendor\livewire\livewire\src\LifecycleManager.php:89
#29 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#30 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#31 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#32 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#33 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#34 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#38 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#40 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#43 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#45 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#49 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
  
[2025-07-08 16:44:32] local.INFO: Model: App\Models\Matiere
Relation: App\Models\Ue
Num-Called: 16
Call-Stack:
#25 \app\Http\Livewire\Etudiant.php:1002
#26 \app\Http\Livewire\Etudiant.php:855
#27 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#28 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#29 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#30 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#31 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#32 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#33 \vendor\livewire\livewire\src\LifecycleManager.php:89
#34 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#35 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#36 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#37 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#38 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#39 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#41 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#42 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#43 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#45 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#47 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#48 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#49 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
  
[2025-07-08 16:44:32] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 16
Call-Stack:
#30 \app\Http\Livewire\Etudiant.php:1002
#31 \app\Http\Livewire\Etudiant.php:855
#32 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#33 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#34 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#35 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#36 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#37 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#38 \vendor\livewire\livewire\src\LifecycleManager.php:89
#39 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#40 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#41 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#42 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#43 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#44 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#46 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#49 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
  
[2025-07-08 16:44:32] local.INFO: Model: App\Models\Note
Relation: App\Models\TypeNote
Num-Called: 16
Call-Stack:
#20 \app\Http\Livewire\Etudiant.php:1002
#21 \app\Http\Livewire\Etudiant.php:855
#22 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#23 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#25 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#26 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#27 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#28 \vendor\livewire\livewire\src\LifecycleManager.php:89
#29 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#30 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#31 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#32 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#33 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#34 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#35 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#36 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#38 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#40 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#43 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#44 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#45 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#49 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
  

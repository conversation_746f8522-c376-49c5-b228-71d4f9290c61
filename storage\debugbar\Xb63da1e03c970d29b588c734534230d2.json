{"__meta": {"id": "Xb63da1e03c970d29b588c734534230d2", "datetime": "2025-07-08 17:20:20", "utime": **********.186524, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751984413.114712, "end": **********.186559, "duration": 7.071846961975098, "duration_str": "7.07s", "measures": [{"label": "Booting", "start": 1751984413.114712, "relative_start": 0, "end": 1751984415.00017, "relative_end": 1751984415.00017, "duration": 1.885457992553711, "duration_str": "1.89s", "params": [], "collector": null}, {"label": "Application", "start": 1751984415.001204, "relative_start": 1.8864920139312744, "end": **********.186562, "relative_end": 3.0994415283203125e-06, "duration": 5.185358047485352, "duration_str": "5.19s", "params": [], "collector": null}]}, "memory": {"peak_usage": 25676792, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=32\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:32-60</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.16119, "accumulated_duration_str": "161ms", "statements": [{"sql": "select * from `users` where `email` = 'tcadmin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["tcadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 86}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.16119, "duration_str": "161ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:139", "connection": "imsaaapp", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": 1}, "count": 1}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T8GodH49oHBvY153WBTZLzzhTxCDGL69GfytSQXe", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1052152706 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7irXq9wtJbT3pXo7VhIsI9RVG1dIn1knYSLZzJlk</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"7 characters\">tcadmin</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Imsaa21?!</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052152706\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-75220405 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">84</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"721 characters\">XSRF-TOKEN=eyJpdiI6IjhsSkRHSTl5bWhQNDVFZnNaK2E5c3c9PSIsInZhbHVlIjoiYytsZXZXSTJsK3dXdzhNandtNUJXQzNTK3FlRkEzYWdhemdxcHN2ZExveDJpc0NwZHBjc09WWm0wVlozQ2xJcnhQRzJRSXlENE4rYlVmN2U3ZDVya1R6SjFKYXAyeTYzK1JwUWs4TTRJNlRnWFdrUnJuTjBqaTZKazcxZXFwakIiLCJtYWMiOiJkMzRjMjYxNTJkNGFlNjAzODg5MjRlNzJjZDVlZWRiN2QwNzRhNGI3NjkzZWJlOTg2MmNlZDMyMDAwNmY3ZjViIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlpzdXp0VDlEblVhYW5lc3RqTURQVWc9PSIsInZhbHVlIjoiUDV6S0NNTE4vcXc4ekVoV1R4TWNLbDU0eGF3dG5OdFBqUHFBZmVCWmVRYTk0eFRlNXFPMHJTQk85S3A2SDBiSjdsTDRQbERYSkhpWFdpcDdPc0VWa0NwcjB1Y0J3ZVB6eitMdUJJc1E1QkRjNjg5dFZ3OXdxQzBuZXBVZ2V5UkgiLCJtYWMiOiJkN2M3ZmM2MWJmYjkyOTQwNmUxY2JhOTA1YzZkMTZjYjhmNGJiODkxMTg5YWM0ZDYwMGY0YmU5NzExZDc0ZmJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75220405\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-987074779 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57270</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">84</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">84</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"721 characters\">XSRF-TOKEN=eyJpdiI6IjhsSkRHSTl5bWhQNDVFZnNaK2E5c3c9PSIsInZhbHVlIjoiYytsZXZXSTJsK3dXdzhNandtNUJXQzNTK3FlRkEzYWdhemdxcHN2ZExveDJpc0NwZHBjc09WWm0wVlozQ2xJcnhQRzJRSXlENE4rYlVmN2U3ZDVya1R6SjFKYXAyeTYzK1JwUWs4TTRJNlRnWFdrUnJuTjBqaTZKazcxZXFwakIiLCJtYWMiOiJkMzRjMjYxNTJkNGFlNjAzODg5MjRlNzJjZDVlZWRiN2QwNzRhNGI3NjkzZWJlOTg2MmNlZDMyMDAwNmY3ZjViIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlpzdXp0VDlEblVhYW5lc3RqTURQVWc9PSIsInZhbHVlIjoiUDV6S0NNTE4vcXc4ekVoV1R4TWNLbDU0eGF3dG5OdFBqUHFBZmVCWmVRYTk0eFRlNXFPMHJTQk85S3A2SDBiSjdsTDRQbERYSkhpWFdpcDdPc0VWa0NwcjB1Y0J3ZVB6eitMdUJJc1E1QkRjNjg5dFZ3OXdxQzBuZXBVZ2V5UkgiLCJtYWMiOiJkN2M3ZmM2MWJmYjkyOTQwNmUxY2JhOTA1YzZkMTZjYjhmNGJiODkxMTg5YWM0ZDYwMGY0YmU5NzExZDc0ZmJkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751984413.1147</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751984413</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987074779\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-941458500 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7irXq9wtJbT3pXo7VhIsI9RVG1dIn1knYSLZzJlk</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nL29mUNQMtbtZg6fzu1W7x95KDVa95Q0CXjhMa7W</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941458500\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1704828848 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 14:20:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImlrZGRWVnN5ZmFvVUlKakQrWVlaNnc9PSIsInZhbHVlIjoieWVTVjd1LzdmVG8zR2NUb1JoQk1YRTlHVC92YndMSkQyczk0c0dCNmRaWFRiemFLWWVOWlc3bVRWZXkxZGJWS25hWWVqS0lJWXVpNEpkR05hMWNZcnhkY0M0T3RnRkxqVGRLSTdkckhxL3FUd0JMdTc4ZVhGakpQVENGSE9wLzQiLCJtYWMiOiJlZTRjNzc1OTVjZjZmZmFjNDdiZTJmZTgxMGViMWZkOGU4ZDFkYzlmNjQ3ZjAzOTY3MDYwMWE0NGU3ZGJlMjM2IiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 16:20:20 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6Ii9iV0dYV2lMaHBpcm4rWVV5SkdGelE9PSIsInZhbHVlIjoiMmVHbkpZVHF0YUVrVjRCc1lZNG9XaC92bmt3N1NTR0R4NDFMaklnaW5MbHVibW5WM0NUYXFSek5rOFRRL3Qwc0x3T3F6QndNN0FEVVFuTkxyMWVod2N1TFFlejVRRzczSDg1WFlJdWNYN1BxL0ZZWjVTYi9WYk50bXh3d2lTWnQiLCJtYWMiOiIyMGMyZWNmNmYxNTUyY2IyZTQ4YmUwZTc3N2M1YTJhZDU3NDc5YzA0YjljNTg2NmFhY2ExYzliNjRiZDgzZDI3IiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 16:20:20 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImlrZGRWVnN5ZmFvVUlKakQrWVlaNnc9PSIsInZhbHVlIjoieWVTVjd1LzdmVG8zR2NUb1JoQk1YRTlHVC92YndMSkQyczk0c0dCNmRaWFRiemFLWWVOWlc3bVRWZXkxZGJWS25hWWVqS0lJWXVpNEpkR05hMWNZcnhkY0M0T3RnRkxqVGRLSTdkckhxL3FUd0JMdTc4ZVhGakpQVENGSE9wLzQiLCJtYWMiOiJlZTRjNzc1OTVjZjZmZmFjNDdiZTJmZTgxMGViMWZkOGU4ZDFkYzlmNjQ3ZjAzOTY3MDYwMWE0NGU3ZGJlMjM2IiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 16:20:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6Ii9iV0dYV2lMaHBpcm4rWVV5SkdGelE9PSIsInZhbHVlIjoiMmVHbkpZVHF0YUVrVjRCc1lZNG9XaC92bmt3N1NTR0R4NDFMaklnaW5MbHVibW5WM0NUYXFSek5rOFRRL3Qwc0x3T3F6QndNN0FEVVFuTkxyMWVod2N1TFFlejVRRzczSDg1WFlJdWNYN1BxL0ZZWjVTYi9WYk50bXh3d2lTWnQiLCJtYWMiOiIyMGMyZWNmNmYxNTUyY2IyZTQ4YmUwZTc3N2M1YTJhZDU3NDc5YzA0YjljNTg2NmFhY2ExYzliNjRiZDgzZDI3IiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 16:20:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704828848\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-174291517 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T8GodH49oHBvY153WBTZLzzhTxCDGL69GfytSQXe</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174291517\", {\"maxDepth\":0})</script>\n"}}
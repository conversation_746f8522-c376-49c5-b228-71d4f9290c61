{"__meta": {"id": "Xb0de53bd0b0cf11603479ac57cdd0709", "datetime": "2025-07-08 16:58:49", "utime": 1751983129.249496, "method": "GET", "uri": "/gestions/etudiants?page=70", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751983071.588614, "end": 1751983129.249564, "duration": 57.66094994544983, "duration_str": "57.66s", "measures": [{"label": "Booting", "start": 1751983071.588614, "relative_start": 0, "end": 1751983079.864287, "relative_end": 1751983079.864287, "duration": 8.275672912597656, "duration_str": "8.28s", "params": [], "collector": null}, {"label": "Application", "start": 1751983080.339409, "relative_start": 8.750795125961304, "end": 1751983129.249569, "relative_end": 5.0067901611328125e-06, "duration": 48.91015982627869, "duration_str": "48.91s", "params": [], "collector": null}]}, "memory": {"peak_usage": 27363296, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src/Macros/livewire-view-extends.blade.php (\\vendor\\livewire\\livewire\\src\\Macros\\livewire-view-extends.blade.php)", "param_count": 4, "params": ["view", "params", "slotOrSection", "manager"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src/Macros/livewire-view-extends.blade.php&line=0"}, {"name": "livewire.deraq.etudiant.index (\\resources\\views\\livewire\\deraq\\etudiant\\index.blade.php)", "param_count": 63, "params": ["etudiants", "parcours", "annees", "niveaux", "livewireLayout", "errors", "_instance", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "showEditNoteModal", "editNote", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/etudiant/index.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "layouts.backend (\\resources\\views\\layouts\\backend.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "view", "params", "slotOrSection", "manager"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/layouts/backend.blade.php&line=0"}, {"name": "components.menu (\\resources\\views\\components\\menu.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/menu.blade.php&line=0"}, {"name": "components.rightHeader (\\resources\\views\\components\\rightHeader.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/rightHeader.blade.php&line=0"}]}, "route": {"uri": "GET gestions/etudiants", "middleware": "web, auth, auth.deraq", "controller": "App\\Http\\Livewire\\Etudiant@__invoke", "as": "deraq.gestions.etudiants.index", "namespace": null, "prefix": "/gestions", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\Component.php&line=46\">\\vendor\\livewire\\livewire\\src\\Component.php:46-88</a>"}, "queries": {"nb_statements": 17, "nb_failed_statements": 0, "accumulated_duration": 1.5435900000000002, "accumulated_duration_str": "1.54s", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.7719600000000001, "duration_str": "772ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 50.011}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.043429999999999996, "duration_str": "43.43ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 50.011, "width_percent": 2.814}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 145}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 61}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.11367000000000001, "duration_str": "114ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:145", "connection": "imsaaapp", "start_percent": 52.824, "width_percent": 7.364}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is not null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 146}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 61}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00767, "duration_str": "7.67ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:146", "connection": "imsaaapp", "start_percent": 60.188, "width_percent": 0.497}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00844, "duration_str": "8.44ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 60.685, "width_percent": 0.547}, {"sql": "select `id`, `user_id`, `parcour_id`, `niveau_id`, `annee_universitaire_id`, `created_at`, `deleted_at` from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null order by `created_at` desc limit 10 offset 690", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.06842000000000001, "duration_str": "68.42ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 61.232, "width_percent": 4.433}, {"sql": "select `id`, `nom`, `prenom`, `telephone1`, `sexe`, `photo` from `users` where `users`.`id` in (84, 85, 86, 87, 88, 89, 109, 110, 129, 130) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.06518, "duration_str": "65.18ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 65.664, "width_percent": 4.223}, {"sql": "select `id`, `sigle`, `nom` from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.03671, "duration_str": "36.71ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 69.887, "width_percent": 2.378}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00429, "duration_str": "4.29ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 72.265, "width_percent": 0.278}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.016309999999999998, "duration_str": "16.31ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 72.543, "width_percent": 1.057}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.027489999999999997, "duration_str": "27.49ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 73.6, "width_percent": 1.781}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.06922, "duration_str": "69.22ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 75.381, "width_percent": 4.484}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.023399999999999997, "duration_str": "23.4ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 79.865, "width_percent": 1.516}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.07953, "duration_str": "79.53ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 81.381, "width_percent": 5.152}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.0189, "duration_str": "18.9ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 86.533, "width_percent": 1.224}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.10185, "duration_str": "102ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 87.758, "width_percent": 6.598}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Helpers\\helpers.php", "line": 18}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "duration": 0.08712, "duration_str": "87.12ms", "stmt_id": "\\app\\Helpers\\helpers.php:18", "connection": "imsaaapp", "start_percent": 94.356, "width_percent": 5.644}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 1, "App\\Models\\Niveau": 1, "App\\Models\\Parcour": 1, "App\\Models\\InscriptionStudent": 10, "App\\Models\\Role": 8, "App\\Models\\User": 11}, "count": 32}, "livewire": {"data": {"etudiant #8MT364czZjVuAYFLykfe": "array:5 [\n  \"data\" => array:56 [\n    \"showCreateModal\" => false\n    \"showEditModal\" => false\n    \"showDeleteModal\" => false\n    \"showRestoreModal\" => false\n    \"viewSupprimesMode\" => false\n    \"selectedEtudiantId\" => null\n    \"selectedUserId\" => null\n    \"etuName\" => null\n    \"newUser\" => []\n    \"editUser\" => []\n    \"editParcours\" => []\n    \"query\" => null\n    \"filtreParcours\" => null\n    \"filtreNiveau\" => null\n    \"filtreAnnee\" => null\n    \"perPage\" => 10\n    \"isLoading\" => false\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"showFilters\" => true\n    \"compactView\" => false\n    \"bulkAction\" => \"\"\n    \"totalEtudiants\" => 720\n    \"totalSupprimés\" => 102\n    \"selectedItems\" => []\n    \"showNotes\" => false\n    \"selectedStudentName\" => \"\"\n    \"noteUserId\" => null\n    \"noteParcourId\" => null\n    \"noteMatiereId\" => null\n    \"noteTypeId\" => null\n    \"noteValeur\" => null\n    \"noteObservation\" => null\n    \"noteActionMode\" => \"list\"\n    \"selectedNoteId\" => null\n    \"notes\" => []\n    \"matieres\" => []\n    \"noteTypes\" => []\n    \"current_semestre\" => null\n    \"current_niveau\" => null\n    \"current_annee\" => null\n    \"current_parcour\" => null\n    \"selectedSemestreId\" => \"\"\n    \"viewMode\" => \"table\"\n    \"searchQuery\" => \"\"\n    \"sortBy\" => \"created_at_desc\"\n    \"selectedNoteType\" => \"\"\n    \"autoSave\" => true\n    \"saveStatus\" => []\n    \"notesStatistics\" => []\n    \"validationErrors\" => []\n    \"isValidating\" => false\n    \"showEditNoteModal\" => false\n    \"editNote\" => []\n    \"page\" => 70\n    \"paginators\" => array:1 [\n      \"page\" => \"70\"\n    ]\n  ]\n  \"name\" => \"etudiant\"\n  \"view\" => \"livewire.deraq.etudiant.index\"\n  \"component\" => \"App\\Http\\Livewire\\Etudiant\"\n  \"id\" => \"8MT364czZjVuAYFLykfe\"\n]"}, "count": 1}, "gate": {"count": 7, "messages": [{"message": "[ability => deraq, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-805549260 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">deraq</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-805549260\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751983109.41771}, {"message": "[ability => admin, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1985532303 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985532303\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751983127.915378}, {"message": "[ability => enseignant, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-203957061 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">enseignant</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203957061\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751983128.055287}, {"message": "[ability => deraq, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1765941186 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">deraq</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1765941186\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751983128.084477}, {"message": "[ability => caf, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2006164614 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2006164614\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751983128.170431}, {"message": "[ability => secretaire, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-258821446 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">secretaire</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-258821446\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751983128.194826}, {"message": "[ability => etudiant, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1613241902 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613241902\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751983128.302676}]}, "session": {"_token": "FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestions/etudiants?page=70\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751977751\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/gestions/etudiants", "status_code": "<pre class=sf-dump id=sf-dump-231443535 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-231443535\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-259511088 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-259511088\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-436602977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-436602977\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1607393514 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/habilitations/utilisateurs?page=4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlBlQUUxWHVMdFJwWndoMnB1TVVValE9PSIsInZhbHVlIjoiWG05TW5oa0E2cUVyaXhlRTFybDdlNEorZjM1TnBhd0plYXJ0K0kwUlBoQ3NsVHd1QWhNdmYxUmxmTUl6QVFrN1ZJRzlJSno5SlhJTVhDUXNZZGpmbGdKUEZLMkwwbGZMWXNmWE96ZDJVUEo5NFZQT1dxOGdJRXJrWnlEbXRiMTMiLCJtYWMiOiIxYjYwNTBiNmNmOTBhMmNlMTIxNmYzYzQ4OWE2OWU2NjAwYTA1MTUzMGFhNGQ5Nzc0M2U0ZjIyYjlmN2YzMGU1IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlhVZEgxM25xbkRxZ3BkeGlIN0kvbFE9PSIsInZhbHVlIjoib1VreHBpMnV3emMrQzlqMkkxU1lwekZHL3MxMktzV091dFgzM1lKTGJMQlRXcEhuOE1NUmI4Qkhyei9zTk5rNFR6QlZTS0pVc3pPS01JQ09QVng0Y2VWVyswT3hZemZMZWVzVkZsOVpMd0RqeWtHa0tmY3NRTkg4R0M2Z0lET1QiLCJtYWMiOiI4ZWVjM2ZjYWE5ZjYzOTVkMWE0NjU1YmY0NjQ5Njk3NmU3ZmRlZGY1ZmUzZjZiMTcwZWQ3YzE5OGE2NzQ5YjdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607393514\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1014089536 data-indent-pad=\"  \"><span class=sf-dump-note>array:33</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56025</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/gestions/etudiants?page=70</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/gestions/etudiants</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/gestions/etudiants</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"7 characters\">page=70</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/habilitations/utilisateurs?page=4</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlBlQUUxWHVMdFJwWndoMnB1TVVValE9PSIsInZhbHVlIjoiWG05TW5oa0E2cUVyaXhlRTFybDdlNEorZjM1TnBhd0plYXJ0K0kwUlBoQ3NsVHd1QWhNdmYxUmxmTUl6QVFrN1ZJRzlJSno5SlhJTVhDUXNZZGpmbGdKUEZLMkwwbGZMWXNmWE96ZDJVUEo5NFZQT1dxOGdJRXJrWnlEbXRiMTMiLCJtYWMiOiIxYjYwNTBiNmNmOTBhMmNlMTIxNmYzYzQ4OWE2OWU2NjAwYTA1MTUzMGFhNGQ5Nzc0M2U0ZjIyYjlmN2YzMGU1IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlhVZEgxM25xbkRxZ3BkeGlIN0kvbFE9PSIsInZhbHVlIjoib1VreHBpMnV3emMrQzlqMkkxU1lwekZHL3MxMktzV091dFgzM1lKTGJMQlRXcEhuOE1NUmI4Qkhyei9zTk5rNFR6QlZTS0pVc3pPS01JQ09QVng0Y2VWVyswT3hZemZMZWVzVkZsOVpMd0RqeWtHa0tmY3NRTkg4R0M2Z0lET1QiLCJtYWMiOiI4ZWVjM2ZjYWE5ZjYzOTVkMWE0NjU1YmY0NjQ5Njk3NmU3ZmRlZGY1ZmUzZjZiMTcwZWQ3YzE5OGE2NzQ5YjdjIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751983071.5886</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751983071</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014089536\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1011447630 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IC6ylzyOLsmp8eqEPKRtYbNYwxNcQ3yBFxnhfM9e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011447630\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1851116888 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 13:58:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJxUVJIaFU4UnR0aVZ2am9qNWhacVE9PSIsInZhbHVlIjoidkdQekE2eXI4ZTFFVzljUTA0S0lKSmtneXFSVWpYMnpNSloxMnNtODl2cGM5TzA0MkxHWDBFQVp4eTRQQWJjSS8yTzNDVDJ1Vy82RUROQ1o0bjZic1hocDlmS3B1OGxCT2JVTVpHMEcxZ0xjckVrZ3dFanBWUkU0c2lBZzN5U3kiLCJtYWMiOiJjOTNiMThhNjMyZDhiOGI2NTMwOWFjYTFmNmZkYmQ1YTEwOWE4ODhmNzgwYTk5NzlhZDYxZmJiODMwNjkyNmFlIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 15:58:48 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IkhnWkhDY3BOb0d6Ny9sQk5EbURNc2c9PSIsInZhbHVlIjoiMURYaFNYbFZZbHRHdGVUaUhoMGRpYWkzSFQ0WXhwTzBlMGRCK09CaWRSQ0M5YVpRenlDT0NTaUhUb0toR0NCTUZWWkZGZlg2TDQrWU9GSWV1SDc0blhWMEVIT2xEWEphS0owaTNJcUVTdlBsVEl5T2NhNHZBblR3ZzljcWhwem0iLCJtYWMiOiJhN2I0MzQ2OTUzNzgxZTg4OWExOTQyOGNiMmRkZmQxZGRmN2RhMzY4ZWM4OTc4NjliYTg4Mjc4YTExNzVhMmM0IiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 15:58:48 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJxUVJIaFU4UnR0aVZ2am9qNWhacVE9PSIsInZhbHVlIjoidkdQekE2eXI4ZTFFVzljUTA0S0lKSmtneXFSVWpYMnpNSloxMnNtODl2cGM5TzA0MkxHWDBFQVp4eTRQQWJjSS8yTzNDVDJ1Vy82RUROQ1o0bjZic1hocDlmS3B1OGxCT2JVTVpHMEcxZ0xjckVrZ3dFanBWUkU0c2lBZzN5U3kiLCJtYWMiOiJjOTNiMThhNjMyZDhiOGI2NTMwOWFjYTFmNmZkYmQ1YTEwOWE4ODhmNzgwYTk5NzlhZDYxZmJiODMwNjkyNmFlIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 15:58:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IkhnWkhDY3BOb0d6Ny9sQk5EbURNc2c9PSIsInZhbHVlIjoiMURYaFNYbFZZbHRHdGVUaUhoMGRpYWkzSFQ0WXhwTzBlMGRCK09CaWRSQ0M5YVpRenlDT0NTaUhUb0toR0NCTUZWWkZGZlg2TDQrWU9GSWV1SDc0blhWMEVIT2xEWEphS0owaTNJcUVTdlBsVEl5T2NhNHZBblR3ZzljcWhwem0iLCJtYWMiOiJhN2I0MzQ2OTUzNzgxZTg4OWExOTQyOGNiMmRkZmQxZGRmN2RhMzY4ZWM4OTc4NjliYTg4Mjc4YTExNzVhMmM0IiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 15:58:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851116888\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-961875330 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751977751</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961875330\", {\"maxDepth\":0})</script>\n"}}